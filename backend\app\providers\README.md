# Providers 模块

## 概述

Providers 模块实现了Auto EM v2的外部服务集成架构，支持多种验证码、短信、代理等第三方服务提供商的统一接入。

## 设计理念

### 核心原则
- **抽象接口**: 通过ABC定义统一的Provider接口
- **多Provider支持**: 支持同类型的多个服务提供商
- **可配置**: 通过配置文件灵活切换Provider
- **可测试**: 支持Mock和契约测试
- **容错机制**: 优雅处理外部服务故障

### 架构模式
采用**工厂模式 + 抽象工厂模式**组合：
- `base.py`: 定义抽象接口
- `implementations.py`: 具体实现
- `factory.py`: 创建工厂方法

## 模块结构

```
providers/
├── base.py              # 抽象基类定义
├── implementations.py   # 具体Provider实现
├── factory.py          # Provider工厂
└── README.md           # 本文档
```

## Provider接口规范

### 1. CaptchaProvider (验证码服务)
**职责**: 验证码识别和处理

**接口定义**:
```python
class CaptchaProvider(ABC):
    @abstractmethod
    def solve(self, *, image_base64: Optional[str] = None, 
             site_key: Optional[str] = None, 
             context: Optional[dict] = None) -> str:
        """返回验证码解答或令牌"""
    
    @abstractmethod
    def test_connectivity(self) -> dict:
        """返回诊断信息，如余额/状态"""
```

**支持的Provider**:
- `manual`: 手动处理模式
- `2captcha`: 2captcha.com 服务
- `anticaptcha`: anti-captcha.com 服务

**配置示例**:
```python
# Manual模式（开发/测试）
provider = create_captcha_provider("manual", {})

# 2captcha服务
provider = create_captcha_provider("2captcha", {
    "api_key": "your_api_key_here"
})
```

### 2. SmsProvider (短信服务)
**职责**: 短信验证码接收

**接口定义**:
```python
class SmsProvider(ABC):
    @abstractmethod
    def get_number(self, service: str) -> dict:
        """返回 {activation_id, phone_number}"""
    
    @abstractmethod
    def poll_code(self, activation_id: str, *, 
                 max_attempts: int = 60, 
                 interval_seconds: int = 5) -> Optional[str]:
        """轮询验证码，返回验证码字符串或None"""
    
    @abstractmethod
    def set_status(self, activation_id: str, status: int) -> None:
        """更新激活状态 (6=确认, 8=取消)"""
    
    @abstractmethod
    def test_connectivity(self) -> dict:
        """返回余额/状态等诊断信息"""
```

**支持的Provider**:
- `5sim`: 5sim.net 服务
- `sms_activate`: sms-activate.org 服务

**使用示例**:
```python
provider = create_sms_provider("5sim", {
    "api_key": "your_api_key"
})

# 获取号码
number_info = provider.get_number("microsoft")
phone = number_info["phone_number"]
activation_id = number_info["activation_id"]

# 轮询验证码
code = provider.poll_code(activation_id, max_attempts=60)

# 确认使用
if code:
    provider.set_status(activation_id, 6)  # 确认
else:
    provider.set_status(activation_id, 8)  # 取消
```

### 3. ProxyProvider (代理服务)
**职责**: 网络代理管理

**接口定义**:
```python
class ProxyProvider(ABC):
    @abstractmethod
    def next(self) -> Optional[str]:
        """返回下一个可用代理URL或None"""
    
    @abstractmethod
    def test_connectivity(self) -> dict:
        """返回代理池诊断信息"""
```

**支持的Provider**:
- `custom`: 自定义代理列表
- `webshare`: webshare.io 服务

**配置示例**:
```python
# 自定义代理列表
provider = create_proxy_provider("custom", {
    "proxies": "http://proxy1:8080,http://proxy2:8080"
})

# Webshare服务
provider = create_proxy_provider("webshare", {
    "api_key": "your_api_key",
    "endpoint": "https://proxy.webshare.io/api/proxy/list/"
})
```

## Provider实现

### 开发新Provider

1. **实现接口**:
```python
class NewCaptchaProvider(CaptchaProvider):
    def __init__(self, config: dict):
        self.api_key = config.get("api_key")
        self.endpoint = config.get("endpoint", "https://api.example.com")
    
    def solve(self, *, image_base64: Optional[str] = None, 
             site_key: Optional[str] = None, 
             context: Optional[dict] = None) -> str:
        # 实现具体逻辑
        pass
    
    def test_connectivity(self) -> dict:
        # 测试连接
        return {"ok": True, "balance": 10.5}
```

2. **注册到工厂**:
```python
# 在 factory.py 中
def create_captcha_provider(name: str, config: dict) -> CaptchaProvider:
    if name == "new_provider":
        return NewCaptchaProvider(config)
    # ... 其他provider
```

3. **添加测试**:
```python
# 在 tests/contract/test_providers.py 中添加契约测试
```

### Provider配置管理

#### 环境变量配置
```bash
# .env 文件
CAPTCHA_PROVIDER=2captcha
CAPTCHA_API_KEY=your_api_key

SMS_PROVIDER=5sim
SMS_API_KEY=your_sms_key

PROXY_PROVIDER=webshare
PROXY_API_KEY=your_proxy_key
```

#### 程序化配置
```python
from backend.app.providers.factory import (
    create_captcha_provider,
    create_sms_provider,
    create_proxy_provider
)

# 创建Provider实例
captcha = create_captcha_provider("2captcha", {
    "api_key": "your_key"
})

sms = create_sms_provider("5sim", {
    "api_key": "your_key"  
})

proxy = create_proxy_provider("custom", {
    "proxies": "http://p1:8080,http://p2:8080"
})
```

## 错误处理

### 统一异常处理
```python
try:
    result = provider.solve(image_base64=image_data)
except Exception as e:
    logger.error(f"Captcha solving failed: {e}")
    # 降级处理或重试
```

### 连接测试
```python
def validate_provider(provider):
    try:
        result = provider.test_connectivity()
        return result.get("ok", False)
    except Exception:
        return False
```

### 重试机制
```python
def solve_with_retry(provider, image_data, max_retries=3):
    for attempt in range(max_retries):
        try:
            return provider.solve(image_base64=image_data)
        except Exception as e:
            if attempt == max_retries - 1:
                raise
            time.sleep(2 ** attempt)  # 指数退避
```

## 测试策略

### 1. 契约测试
验证所有Provider实现符合接口契约：
```python
@pytest.fixture(params=["manual", "2captcha"])
def captcha_provider(request):
    provider_name = request.param
    return create_captcha_provider(provider_name, config)

def test_solve_method_exists(captcha_provider):
    assert hasattr(captcha_provider, 'solve')
    assert callable(getattr(captcha_provider, 'solve'))
```

### 2. 集成测试
使用真实服务进行端到端测试：
```python
@pytest.mark.network
def test_real_captcha_service():
    provider = create_captcha_provider("2captcha", {
        "api_key": os.getenv("TEST_API_KEY")
    })
    
    connectivity = provider.test_connectivity()
    assert connectivity.get("ok") is True
```

### 3. Mock测试
业务逻辑中使用Mock Provider：
```python
def test_registration_with_captcha(mock_captcha_provider):
    mock_captcha_provider.solve.return_value = "ABCD123"
    
    service = RegistrationPipelineService()
    result = service._solve_captcha("test_image")
    
    assert result == "ABCD123"
```

## 配置参考

### 完整配置示例
```python
PROVIDER_CONFIGS = {
    "captcha": {
        "provider": "2captcha",
        "config": {
            "api_key": "your_2captcha_key",
            "timeout": 120,
            "polling_interval": 5
        }
    },
    "sms": {
        "provider": "5sim", 
        "config": {
            "api_key": "your_5sim_key",
            "country": "russia",
            "operator": "any"
        }
    },
    "proxy": {
        "provider": "custom",
        "config": {
            "proxies": "http://proxy1:8080,http://proxy2:8080",
            "rotation": "round_robin",
            "health_check": True
        }
    }
}
```

### 动态Provider切换
```python
class ProviderManager:
    def __init__(self):
        self.providers = {}
    
    def get_provider(self, service_type: str):
        config = PROVIDER_CONFIGS.get(service_type)
        if not config:
            raise ValueError(f"No config for {service_type}")
        
        provider_name = config["provider"]
        provider_config = config["config"]
        
        if service_type == "captcha":
            return create_captcha_provider(provider_name, provider_config)
        # 其他类型...
```

## 性能优化

### 连接池
```python
class PooledProvider:
    def __init__(self, provider_class, config, pool_size=10):
        self.pool = [provider_class(config) for _ in range(pool_size)]
        self.current = 0
    
    def get_provider(self):
        provider = self.pool[self.current]
        self.current = (self.current + 1) % len(self.pool)
        return provider
```

### 缓存机制
```python
from functools import lru_cache

@lru_cache(maxsize=100)
def cached_solve(provider_type, image_hash):
    # 对相同图片进行缓存
    pass
```

### 异步支持
```python
import asyncio

class AsyncCaptchaProvider:
    async def solve_async(self, image_data):
        # 异步实现
        pass
```

## 监控和日志

### Provider状态监控
```python
class ProviderMonitor:
    def __init__(self):
        self.stats = {
            "requests": 0,
            "successes": 0,
            "failures": 0,
            "avg_response_time": 0
        }
    
    def record_request(self, success: bool, response_time: float):
        self.stats["requests"] += 1
        if success:
            self.stats["successes"] += 1
        else:
            self.stats["failures"] += 1
        
        # 更新平均响应时间
        self._update_avg_response_time(response_time)
```

### 详细日志
```python
import logging

logger = logging.getLogger(__name__)

def solve_with_logging(provider, image_data):
    start_time = time.time()
    try:
        logger.info(f"Starting captcha solve with {provider.__class__.__name__}")
        result = provider.solve(image_base64=image_data)
        
        duration = time.time() - start_time
        logger.info(f"Captcha solved successfully in {duration:.2f}s")
        return result
        
    except Exception as e:
        duration = time.time() - start_time
        logger.error(f"Captcha solve failed after {duration:.2f}s: {e}")
        raise
```

## 故障排除

### 常见问题

1. **Provider创建失败**
   - 检查配置参数是否正确
   - 验证Provider名称拼写
   - 确认所需依赖已安装

2. **API调用失败**
   - 验证API密钥有效性
   - 检查账户余额
   - 确认网络连接

3. **超时问题**
   - 调整超时参数
   - 检查网络延迟
   - 考虑使用重试机制

### 调试工具

```python
def debug_provider(provider):
    """Provider调试工具"""
    print(f"Provider: {provider.__class__.__name__}")
    
    # 测试连接
    try:
        result = provider.test_connectivity()
        print(f"Connectivity: {result}")
    except Exception as e:
        print(f"Connectivity failed: {e}")
    
    # 显示配置
    if hasattr(provider, 'config'):
        safe_config = {k: v if k != 'api_key' else '***' 
                      for k, v in provider.config.items()}
        print(f"Config: {safe_config}")
```

---

## 依赖管理

### 外部依赖
- `requests`: HTTP客户端
- `abc`: 抽象基类支持
- 各Provider特定的SDK (可选)

### 内部依赖
- `backend.app.core.logging`: 日志记录
- 无其他强依赖，保持模块独立性

---

Providers模块通过统一的抽象接口和灵活的工厂模式，为Auto EM v2提供了强大的外部服务集成能力。模块化的设计使得添加新Provider和切换服务商变得简单便捷，同时保持了良好的测试性和可维护性。