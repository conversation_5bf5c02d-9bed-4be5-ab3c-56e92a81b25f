#!/usr/bin/env python3
"""
测试运行脚本 - 便于本地开发时运行不同类型的测试
"""
import argparse
import subprocess
import sys
from pathlib import Path


def run_command(cmd, description=""):
    """运行命令并处理错误"""
    print(f"\n🔄 {description or 'Running command'}: {' '.join(cmd)}")
    try:
        result = subprocess.run(cmd, check=True, capture_output=False)
        print(f"✅ {description or 'Command'} completed successfully")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ {description or 'Command'} failed with exit code {e.returncode}")
        return False


def main():
    parser = argparse.ArgumentParser(description="运行项目测试")
    parser.add_argument(
        "--type", "-t", choices=["unit", "integration", "contract", "all"], default="all", help="要运行的测试类型"
    )
    parser.add_argument("--coverage", "-c", action="store_true", help="生成覆盖率报告")
    parser.add_argument("--verbose", "-v", action="store_true", help="详细输出")
    parser.add_argument("--lint", "-l", action="store_true", help="运行代码检查")
    parser.add_argument("--format", "-f", action="store_true", help="检查代码格式")

    args = parser.parse_args()

    # 确保在项目根目录
    project_root = Path(__file__).parent
    sys.path.insert(0, str(project_root))

    success = True

    # 代码检查
    if args.lint:
        success &= run_command(["ruff", "check", "backend/", "tests/"], "Running Ruff linting")

    # 格式检查
    if args.format:
        success &= run_command(["black", "--check", "backend/", "tests/"], "Checking code formatting with Black")

    # 基础pytest命令
    pytest_cmd = ["pytest", "-c", "config/pytest.ini"]

    if args.verbose:
        pytest_cmd.append("-v")

    if args.coverage:
        pytest_cmd.extend(["--cov=backend", "--cov-report=term-missing", "--cov-report=html"])

    # 根据类型选择测试
    if args.type == "unit":
        pytest_cmd.extend(["tests/unit/", "-m", "unit"])
        success &= run_command(pytest_cmd, "Running unit tests")
    elif args.type == "integration":
        pytest_cmd.extend(["tests/integration/", "-m", "integration"])
        success &= run_command(pytest_cmd, "Running integration tests")
    elif args.type == "contract":
        pytest_cmd.extend(["tests/contract/", "-m", "contract"])
        success &= run_command(pytest_cmd, "Running contract tests")
    elif args.type == "all":
        # 分别运行不同类型的测试
        test_types = [
            (["tests/unit/", "-m", "unit"], "Running unit tests"),
            (["tests/contract/", "-m", "contract"], "Running contract tests"),
            (["tests/integration/", "-m", "integration"], "Running integration tests"),
        ]

        for test_args, description in test_types:
            cmd = pytest_cmd + test_args
            success &= run_command(cmd, description)

    if success:
        print("\n🎉 All tests completed successfully!")
        return 0
    else:
        print("\n💥 Some tests failed!")
        return 1


if __name__ == "__main__":
    sys.exit(main())
