# -*- mode: python ; coding: utf-8 -*-
"""
PyInstaller spec文件 - 用于打包Auto EM v2应用
"""
import os
import sys

block_cipher = None

# 添加backend目录到Python路径
backend_path = os.path.join(SPECPATH, 'backend')
if backend_path not in sys.path:
    sys.path.insert(0, backend_path)

# 数据文件和隐藏导入
datas = [
    # 配置文件
    ('config/.env.example', 'config/'),
    # 静态资源（如果有的话）
    # ('static/', 'static/'),
]

# 隐藏导入 - 确保所有必要的模块被包含
hiddenimports = [
    'uvicorn.loops.auto',
    'uvicorn.protocols.http.auto',
    'uvicorn.protocols.websockets.auto',
    'uvicorn.lifespan.on',
    'sqlmodel',
    'psycopg2',
    'alembic',
    'backend.app.core.config',
    'backend.app.core.logging',
    'backend.app.db.models',
    'backend.app.db.session',
    'backend.app.services.task_engine',
    'backend.app.services.registration_pipeline',
    'backend.app.providers.factory',
    'backend.app.providers.implementations',
    'backend.app.repositories.task_repository',
    'backend.app.repositories.account_repository',
    'backend.app.repositories.provider_config_repository',
]

# 排除的模块
excludes = [
    'tkinter',
    'matplotlib',
    'PIL',
    'numpy',
    'scipy',
    'pandas',
    'jupyter',
    'IPython',
]

a = Analysis(
    ['backend/main.py'],
    pathex=[SPECPATH, backend_path],
    binaries=[],
    datas=datas,
    hiddenimports=hiddenimports,
    hookspath=[],
    hooksconfig={},
    runtime_hooks=[],
    excludes=excludes,
    win_no_prefer_redirects=False,
    win_private_assemblies=False,
    cipher=block_cipher,
    noarchive=False,
)

pyz = PYZ(a.pure, a.zipped_data, cipher=block_cipher)

exe = EXE(
    pyz,
    a.scripts,
    a.binaries,
    a.zipfiles,
    a.datas,
    [],
    name='auto-em-v2',
    debug=False,
    bootloader_ignore_signals=False,
    strip=False,
    upx=True,
    upx_exclude=[],
    runtime_tmpdir=None,
    console=True,
    disable_windowed_traceback=False,
    argv_emulation=False,
    target_arch=None,
    codesign_identity=None,
    entitlements_file=None,
    icon=None  # 可以添加icon文件路径
)