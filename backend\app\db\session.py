from __future__ import annotations

from collections.abc import Iterator

from sqlmodel import Session, SQLModel, create_engine
from sqlalchemy import Engine


def get_sqlite_engine(db_path: str = "auto_em_v2.db") -> Engine:
    # SQLite in local file; check_same_thread=False to allow usage in thread pool tasks
    return create_engine(f"sqlite:///{db_path}", echo=False, connect_args={"check_same_thread": False})


engine = get_sqlite_engine()


def init_db() -> None:
    # Import models to ensure they are registered before table creation
    from backend.app.db import models  # noqa: F401

    SQLModel.metadata.create_all(engine)


def get_session() -> Iterator[Session]:
    with Session(engine) as session:
        yield session
