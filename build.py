#!/usr/bin/env python3
"""
构建脚本 - 用于打包Auto EM v2应用
支持PyInstaller、cx_Freeze和py2exe（Windows）
"""
import argparse
import os
import platform
import shutil
import subprocess
import sys
from pathlib import Path


def run_command(cmd, description="", cwd=None):
    """运行命令并处理错误"""
    print(f"\n🔄 {description}: {' '.join(cmd)}")
    try:
        result = subprocess.run(cmd, check=True, cwd=cwd, capture_output=False)
        print(f"✅ {description} completed successfully")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ {description} failed with exit code {e.returncode}")
        return False


def clean_build_dirs():
    """清理构建目录"""
    dirs_to_clean = ["build", "dist", "__pycache__", "*.spec"]

    for pattern in dirs_to_clean:
        if pattern.startswith("*"):
            # 处理通配符模式
            import glob

            for path in glob.glob(pattern):
                if os.path.isfile(path):
                    os.remove(path)
                    print(f"🗑️ Removed file: {path}")
        else:
            if os.path.exists(pattern):
                shutil.rmtree(pattern)
                print(f"🗑️ Removed directory: {pattern}")


def build_with_pyinstaller(onefile=True, console=True):
    """使用PyInstaller打包"""
    print("\n📦 Building with PyInstaller...")

    cmd = [
        "pyinstaller",
        "--name",
        "auto-em-v2",
        "--clean",
        "--noconfirm",
    ]

    if onefile:
        cmd.append("--onefile")
    else:
        cmd.append("--onedir")

    if console:
        cmd.append("--console")
    else:
        cmd.append("--windowed")

    # 添加隐藏导入
    hidden_imports = [
        "uvicorn.loops.auto",
        "uvicorn.protocols.http.auto",
        "uvicorn.protocols.websockets.auto",
        "backend.app.core.config",
        "backend.app.db.models",
        "backend.app.services.task_engine",
        "backend.app.services.registration_pipeline",
        "backend.app.providers.factory",
    ]

    for imp in hidden_imports:
        cmd.extend(["--hidden-import", imp])

    # 添加数据文件
    if os.path.exists(".env.example"):
        cmd.extend(["--add-data", ".env.example:.:"])

    # 排除不需要的模块
    excludes = ["tkinter", "matplotlib", "PIL", "numpy", "scipy", "pandas"]
    for exc in excludes:
        cmd.extend(["--exclude-module", exc])

    cmd.append("backend/main.py")

    return run_command(cmd, "PyInstaller build")


def build_with_cx_freeze():
    """使用cx_Freeze打包"""
    print("\n📦 Building with cx_Freeze...")

    setup_script = """
import sys
from cx_Freeze import setup, Executable

# 依赖包
packages = [
    'fastapi', 'uvicorn', 'sqlmodel', 'psycopg2', 'alembic', 
    'requests', 'pydantic', 'structlog'
]

# 包含文件
include_files = []
if os.path.exists('.env.example'):
    include_files.append('.env.example')

# 排除包
excludes = ['tkinter', 'matplotlib', 'PIL', 'numpy', 'scipy', 'pandas']

# 构建选项
build_options = {
    'packages': packages,
    'excludes': excludes,
    'include_files': include_files,
    'zip_include_packages': '*',
    'zip_exclude_packages': '',
}

# 执行文件配置
executables = [
    Executable(
        'backend/main.py',
        target_name='auto-em-v2.exe' if sys.platform == 'win32' else 'auto-em-v2',
        base='Console'
    )
]

setup(
    name='Auto EM v2',
    version='2.0.0',
    description='自动邮箱注册工具 v2',
    options={'build_exe': build_options},
    executables=executables
)
"""

    # 写入临时setup.py
    with open("setup_cx.py", "w", encoding="utf-8") as f:
        f.write(setup_script)

    cmd = [sys.executable, "setup_cx.py", "build"]
    success = run_command(cmd, "cx_Freeze build")

    # 清理临时文件
    if os.path.exists("setup_cx.py"):
        os.remove("setup_cx.py")

    return success


def build_with_py2exe():
    """使用py2exe打包（仅Windows）"""
    if platform.system() != "Windows":
        print("⚠️ py2exe is only available on Windows")
        return False

    print("\n📦 Building with py2exe...")

    setup_script = """
from distutils.core import setup
import py2exe

setup(
    console=['backend/main.py'],
    options={
        'py2exe': {
            'bundle_files': 1,
            'compressed': True,
            'optimize': 2,
            'packages': [
                'fastapi', 'uvicorn', 'sqlmodel', 'psycopg2', 
                'alembic', 'requests', 'pydantic', 'structlog'
            ],
            'excludes': ['tkinter', 'matplotlib', 'PIL', 'numpy', 'scipy', 'pandas'],
        }
    },
    zipfile=None,
)
"""

    # 写入临时setup.py
    with open("setup_py2exe.py", "w", encoding="utf-8") as f:
        f.write(setup_script)

    cmd = [sys.executable, "setup_py2exe.py", "py2exe"]
    success = run_command(cmd, "py2exe build")

    # 清理临时文件
    if os.path.exists("setup_py2exe.py"):
        os.remove("setup_py2exe.py")

    return success


def create_installer():
    """创建安装程序（可选）"""
    print("\n📦 Creating installer...")
    # 这里可以添加创建安装程序的逻辑
    # 例如使用NSIS、Inno Setup等
    pass


def main():
    parser = argparse.ArgumentParser(description="构建Auto EM v2应用")
    parser.add_argument(
        "--tool", "-t", choices=["pyinstaller", "cx_freeze", "py2exe"], default="pyinstaller", help="打包工具选择"
    )
    parser.add_argument("--onefile", action="store_true", help="生成单文件可执行程序（仅PyInstaller）")
    parser.add_argument("--windowed", action="store_true", help="生成无控制台窗口程序")
    parser.add_argument("--clean", action="store_true", help="构建前清理旧文件")
    parser.add_argument("--installer", action="store_true", help="构建后创建安装程序")

    args = parser.parse_args()

    # 确保在项目根目录
    project_root = Path(__file__).parent
    os.chdir(project_root)

    print(f"🚀 Building Auto EM v2 using {args.tool}")
    print(f"📁 Working directory: {os.getcwd()}")

    # 清理构建目录
    if args.clean:
        print("\n🧹 Cleaning build directories...")
        clean_build_dirs()

    success = False

    # 根据选择的工具进行构建
    if args.tool == "pyinstaller":
        success = build_with_pyinstaller(onefile=args.onefile, console=not args.windowed)
    elif args.tool == "cx_freeze":
        success = build_with_cx_freeze()
    elif args.tool == "py2exe":
        success = build_with_py2exe()

    if success:
        print("\n🎉 Build completed successfully!")

        # 显示构建结果
        if os.path.exists("dist"):
            print("\n📂 Build output in 'dist' directory:")
            for item in os.listdir("dist"):
                item_path = os.path.join("dist", item)
                if os.path.isfile(item_path):
                    size = os.path.getsize(item_path)
                    print(f"  📄 {item} ({size:,} bytes)")
                else:
                    print(f"  📁 {item}/")

        # 创建安装程序
        if args.installer:
            create_installer()

        return 0
    else:
        print("\n💥 Build failed!")
        return 1


if __name__ == "__main__":
    sys.exit(main())
