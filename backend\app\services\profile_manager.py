from __future__ import annotations

import logging
import secrets
import shutil
from datetime import datetime
from pathlib import Path


class ProfileManager:
    """
    资料管理器：处理头像、昵称等资料文件的管理
    """

    def __init__(self, base_path: str = "assets"):
        self.base_path = Path(base_path)
        self.logger = logging.getLogger(__name__)

        # 创建必要的目录结构
        self.avatars_dir = self.base_path / "avatars"
        self.nicknames_dir = self.base_path / "nicknames"
        self.signatures_dir = self.base_path / "signatures"

        for directory in [self.avatars_dir, self.nicknames_dir, self.signatures_dir]:
            directory.mkdir(parents=True, exist_ok=True)

    def get_random_avatar(self, used_avatars: list[str] | None = None) -> str | None:
        """
        获取随机头像文件路径

        Args:
            used_avatars: 已使用的头像列表，避免重复

        Returns:
            头像文件路径，如果没有可用头像返回None
        """
        try:
            # 获取所有头像文件
            avatar_files = [
                f
                for f in self.avatars_dir.iterdir()
                if f.is_file() and f.suffix.lower() in [".jpg", ".jpeg", ".png", ".gif"]
            ]

            if not avatar_files:
                self.logger.warning("No avatar files found in avatars directory")
                return None

            # 过滤掉已使用的头像
            if used_avatars:
                available_avatars = [f for f in avatar_files if f.name not in used_avatars]
                if available_avatars:
                    avatar_files = available_avatars

            # 随机选择一个头像
            selected_avatar = secrets.choice(avatar_files)

            self.logger.info(f"Selected avatar: {selected_avatar.name}")
            return str(selected_avatar)

        except Exception as e:
            self.logger.error(f"Failed to get random avatar: {e}")
            return None

    def get_random_nickname(self, used_nicknames: list[str] | None = None) -> str | None:
        """
        获取随机昵称

        Args:
            used_nicknames: 已使用的昵称列表，避免重复

        Returns:
            随机生成的昵称
        """
        try:
            # 读取昵称文件
            nickname_file = self.nicknames_dir / "nicknames.txt"

            if nickname_file.exists():
                with open(nickname_file, encoding="utf-8") as f:
                    nicknames = [line.strip() for line in f if line.strip()]
            else:
                # 如果没有昵称文件，使用内置的默认昵称列表
                nicknames = self._get_default_nicknames()
                # 保存默认昵称到文件
                self._save_default_nicknames(nickname_file, nicknames)

            if not nicknames:
                self.logger.warning("No nicknames available")
                return None

            # 过滤掉已使用的昵称
            if used_nicknames:
                available_nicknames = [name for name in nicknames if name not in used_nicknames]
                if available_nicknames:
                    nicknames = available_nicknames

            # 随机选择昵称
            selected_nickname = secrets.choice(nicknames)

            self.logger.info(f"Selected nickname: {selected_nickname}")
            return selected_nickname

        except Exception as e:
            self.logger.error(f"Failed to get random nickname: {e}")
            return None

    def get_random_signature(self) -> str | None:
        """
        获取随机个性签名

        Returns:
            随机个性签名
        """
        try:
            # 读取签名文件
            signature_file = self.signatures_dir / "signatures.txt"

            if signature_file.exists():
                with open(signature_file, encoding="utf-8") as f:
                    signatures = [line.strip() for line in f if line.strip()]
            else:
                # 使用默认签名
                signatures = self._get_default_signatures()
                self._save_default_signatures(signature_file, signatures)

            if not signatures:
                return None

            selected_signature = secrets.choice(signatures)

            self.logger.info(f"Selected signature: {selected_signature[:20]}...")
            return selected_signature

        except Exception as e:
            self.logger.error(f"Failed to get random signature: {e}")
            return None

    def copy_avatar_to_temp(self, avatar_path: str) -> str | None:
        """
        将头像复制到临时目录，用于上传

        Args:
            avatar_path: 头像文件路径

        Returns:
            临时文件路径
        """
        try:
            source_path = Path(avatar_path)
            if not source_path.exists():
                self.logger.error(f"Avatar file not found: {avatar_path}")
                return None

            # 创建临时目录
            temp_dir = self.base_path / "temp"
            temp_dir.mkdir(exist_ok=True)

            # 生成唯一的临时文件名
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            temp_filename = f"avatar_{timestamp}{source_path.suffix}"
            temp_path = temp_dir / temp_filename

            # 复制文件
            shutil.copy2(source_path, temp_path)

            self.logger.info(f"Avatar copied to temp: {temp_path}")
            return str(temp_path)

        except Exception as e:
            self.logger.error(f"Failed to copy avatar to temp: {e}")
            return None

    def cleanup_temp_files(self, max_age_hours: int = 24):
        """
        清理临时文件

        Args:
            max_age_hours: 文件最大保留时间（小时）
        """
        try:
            temp_dir = self.base_path / "temp"
            if not temp_dir.exists():
                return

            cutoff_time = datetime.now().timestamp() - (max_age_hours * 3600)

            for file_path in temp_dir.iterdir():
                if file_path.is_file() and file_path.stat().st_mtime < cutoff_time:
                    file_path.unlink()
                    self.logger.info(f"Cleaned up temp file: {file_path.name}")

        except Exception as e:
            self.logger.error(f"Failed to cleanup temp files: {e}")

    def get_stats(self) -> dict[str, int]:
        """
        获取资料库统计信息

        Returns:
            包含各类资料数量的字典
        """
        try:
            stats = {}

            # 头像数量
            avatar_count = len(
                [
                    f
                    for f in self.avatars_dir.iterdir()
                    if f.is_file() and f.suffix.lower() in [".jpg", ".jpeg", ".png", ".gif"]
                ]
            )
            stats["avatars"] = avatar_count

            # 昵称数量
            nickname_file = self.nicknames_dir / "nicknames.txt"
            if nickname_file.exists():
                with open(nickname_file, encoding="utf-8") as f:
                    nickname_count = len([line.strip() for line in f if line.strip()])
            else:
                nickname_count = len(self._get_default_nicknames())
            stats["nicknames"] = nickname_count

            # 签名数量
            signature_file = self.signatures_dir / "signatures.txt"
            if signature_file.exists():
                with open(signature_file, encoding="utf-8") as f:
                    signature_count = len([line.strip() for line in f if line.strip()])
            else:
                signature_count = len(self._get_default_signatures())
            stats["signatures"] = signature_count

            return stats

        except Exception as e:
            self.logger.error(f"Failed to get stats: {e}")
            return {}

    def _get_default_nicknames(self) -> list[str]:
        """获取默认昵称列表"""
        return [
            "阳光小屋",
            "微风轻语",
            "星辰大海",
            "花开半夏",
            "时光荏苒",
            "岁月静好",
            "春暖花开",
            "秋高气爽",
            "冬日暖阳",
            "夏日清凉",
            "梦想起航",
            "勇敢前行",
            "青春无悔",
            "未来可期",
            "心怀梦想",
            "温柔岁月",
            "静好时光",
            "美好明天",
            "希望之光",
            "快乐每天",
        ]

    def _get_default_signatures(self) -> list[str]:
        """获取默认签名列表"""
        return [
            "生活不止眼前的苟且，还有诗和远方。",
            "愿你三冬暖，愿你春不寒。",
            "时光荏苒，岁月如歌。",
            "心有猛虎，细嗅蔷薇。",
            "愿所有的美好如约而至。",
            "做自己的太阳，无需凭借谁的光。",
            "岁月不居，时节如流。",
            "山河不足重，重在遇知己。",
            "愿你眼中有星辰，心中有山海。",
            "所有的相遇都是久别重逢。",
        ]

    def _save_default_nicknames(self, file_path: Path, nicknames: list[str]):
        """保存默认昵称到文件"""
        try:
            with open(file_path, "w", encoding="utf-8") as f:
                for nickname in nicknames:
                    f.write(f"{nickname}\n")
            self.logger.info(f"Default nicknames saved to {file_path}")
        except Exception as e:
            self.logger.error(f"Failed to save default nicknames: {e}")

    def _save_default_signatures(self, file_path: Path, signatures: list[str]):
        """保存默认签名到文件"""
        try:
            with open(file_path, "w", encoding="utf-8") as f:
                for signature in signatures:
                    f.write(f"{signature}\n")
            self.logger.info(f"Default signatures saved to {file_path}")
        except Exception as e:
            self.logger.error(f"Failed to save default signatures: {e}")
