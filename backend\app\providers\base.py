from __future__ import annotations

from abc import ABC, abstractmethod


class CaptchaProvider(ABC):
    @abstractmethod
    def solve(
        self, *, image_base64: str | None = None, site_key: str | None = None, context: dict | None = None
    ) -> str:
        """Return captcha solution text/token or raise error."""

    @abstractmethod
    def test_connectivity(self) -> dict:
        """Return diagnostic info, e.g., balance/status."""


class SmsProvider(ABC):
    @abstractmethod
    def get_number(self, service: str) -> dict:
        """Return {activation_id, phone_number}."""

    @abstractmethod
    def poll_code(self, activation_id: str, *, max_attempts: int = 60, interval_seconds: int = 5) -> str | None:
        """Return verification code string or None if timeout."""

    @abstractmethod
    def set_status(self, activation_id: str, status: int) -> None:
        """Provider-specific status update (e.g., 6 confirm, 8 cancel)."""

    @abstractmethod
    def test_connectivity(self) -> dict:
        """Return diagnostic info, e.g., balance/status."""


class ProxyProvider(ABC):
    @abstractmethod
    def next(self) -> str | None:
        """Return next proxy URL (http/https) or None."""

    @abstractmethod
    def test_connectivity(self) -> dict:
        """Return diagnostic info."""
