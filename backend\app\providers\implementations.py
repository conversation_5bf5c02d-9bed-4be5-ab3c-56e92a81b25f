from __future__ import annotations

import secrets

import requests

from backend.app.providers.base import <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>xyProvider, SmsProvider


class TwoCaptchaProvider(CaptchaProvider):
    def __init__(self, api_key: str) -> None:
        self.api_key = api_key

    def solve(
        self, *, image_base64: str | None = None, site_key: str | None = None, context: dict | None = None
    ) -> str:
        # Placeholder: only connectivity test in P0; real flow will be added in pipeline task
        if not self.api_key:
            raise ValueError("2captcha api_key missing")
        return "PLACEHOLDER_SOLUTION"

    def test_connectivity(self) -> dict:
        try:
            resp = requests.get(
                "http://2captcha.com/res.php",
                params={"key": self.api_key, "action": "getbalance", "json": 1},
                timeout=10,
            )
            data = resp.json()
            ok = data.get("status") == 1
            return {"ok": ok, "balance": data.get("request") if ok else None, "raw": data}
        except Exception as e:
            return {"ok": False, "error": str(e)}


class SmsActivateProvider(SmsProvider):
    def __init__(self, api_key: str) -> None:
        self.api_key = api_key

    def get_number(self, service: str) -> dict:
        raise NotImplementedError("Implement in registration pipeline task")

    def poll_code(self, activation_id: str, *, max_attempts: int = 60, interval_seconds: int = 5) -> str | None:
        raise NotImplementedError("Implement in registration pipeline task")

    def set_status(self, activation_id: str, status: int) -> None:
        raise NotImplementedError("Implement in registration pipeline task")

    def test_connectivity(self) -> dict:
        try:
            resp = requests.get(
                "https://sms-activate.org/stubs/handler_api.php",
                params={"api_key": self.api_key, "action": "getBalance"},
                timeout=10,
            )
            text = resp.text.strip()
            ok = text.startswith("ACCESS_BALANCE")
            balance = text.split(":")[1] if ok and ":" in text else None
            return {"ok": ok, "balance": balance, "raw": text}
        except Exception as e:
            return {"ok": False, "error": str(e)}


class CustomProxyProvider(ProxyProvider):
    def __init__(self, proxies_csv: str) -> None:
        self.pool = [p.strip() for p in proxies_csv.split(",") if p.strip()] if proxies_csv else []

    def next(self) -> str | None:
        if not self.pool:
            return None
        return secrets.choice(self.pool)

    def test_connectivity(self) -> dict:
        return {"ok": bool(self.pool), "size": len(self.pool)}
