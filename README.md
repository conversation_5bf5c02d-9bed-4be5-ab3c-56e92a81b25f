# Auto EM v2

![Version](https://img.shields.io/badge/version-2.0.0-blue.svg)
![Python](https://img.shields.io/badge/python-3.11+-green.svg)
![License](https://img.shields.io/badge/license-MIT-blue.svg)

自动邮箱注册工具 v2 - 多Provider支持与任务化引擎

## 🚀 功能特性

### 核心功能
- **自动邮箱注册**：支持多平台邮箱自动注册（Outlook、Gmail等）
- **多Provider架构**：支持多种验证码、短信、代理服务提供商
- **任务化引擎**：异步任务处理，支持任务状态追踪
- **账户养护**：周期性登录、邮件标记已读、资料更新
- **群发邮件**：模板化群发，智能限速，账号池轮询

### 技术特性
- **FastAPI Web框架**：现代化API设计，自动文档生成
- **SQLModel ORM**：类型安全的数据库操作
- **Provider模式**：可扩展的服务提供商架构
- **全面测试**：单元测试、集成测试、契约测试
- **CI/CD**：GitHub Actions自动化构建和测试

## 📦 快速开始

### 环境要求

- Python 3.11+
- PostgreSQL 13+ (可选，默认使用SQLite)
- Poetry 或 pip 包管理

### 安装方式

#### 方式1：使用Poetry（推荐）

```bash
# 克隆项目
git clone https://github.com/your-org/auto-em-v2.git
cd auto-em-v2

# 安装依赖
poetry install

# 激活虚拟环境
poetry shell

# 启动应用
poetry run python start.py server
```

#### 方式2：使用pip

```bash
# 克隆项目
git clone https://github.com/your-org/auto-em-v2.git
cd auto-em-v2

# 创建虚拟环境
python -m venv venv
source venv/bin/activate  # Linux/Mac
# venv\Scripts\activate   # Windows

# 安装依赖
pip install -r requirements.txt

# 启动应用
python start.py server
```

### 配置设置

1. 复制环境配置文件：
```bash
cp .env.example .env
```

2. 编辑配置文件 `.env`：
```env
# 数据库配置
DATABASE_URL=sqlite:///./auto_em_v2.db

# 服务器配置
SERVER_HOST=127.0.0.1
SERVER_PORT=8000

# Provider配置
CAPTCHA_PROVIDER=manual
SMS_PROVIDER=5sim
PROXY_PROVIDER=custom
```

3. 初始化数据库：
```bash
python start.py init
```

4. 启动服务器：
```bash
python start.py server
```

应用将在 http://127.0.0.1:8000 启动，API文档可通过 http://127.0.0.1:8000/docs 访问。

## 🏗️ 项目架构

```
auto-em-v2/
├── backend/                    # 后端代码
│   ├── app/                   # 应用核心
│   │   ├── core/             # 核心配置和日志
│   │   ├── db/               # 数据库模型和会话
│   │   ├── providers/        # Provider接口和实现
│   │   ├── repositories/     # 数据访问层
│   │   ├── schemas/          # 数据模型定义
│   │   └── services/         # 业务逻辑服务
│   ├── main.py               # FastAPI应用入口
│   └── cli.py                # 命令行接口
├── tests/                     # 测试代码
│   ├── unit/                 # 单元测试
│   ├── integration/          # 集成测试
│   └── contract/             # Provider契约测试
├── assets/                    # 静态资源
│   ├── avatars/              # 头像文件
│   ├── nicknames/            # 昵称库
│   └── signatures/           # 签名库
├── .github/workflows/         # CI/CD配置
├── build.py                  # 构建脚本
├── start.py                  # 启动脚本
└── README.md                 # 项目文档
```

## 📚 使用指南

### API接口

#### 注册任务
```bash
# 启动邮箱注册任务
curl -X POST "http://localhost:8000/tasks/start/reg" \
  -H "Content-Type: application/json" \
  -d '{"email": "<EMAIL>"}'
```

#### 养护任务
```bash
# 启动账户养护任务
curl -X POST "http://localhost:8000/tasks/start/nurture" \
  -H "Content-Type: application/json" \
  -d '{
    "account_id": 1,
    "nurture_type": "login_check"
  }'
```

#### 群发邮件
```bash
# 启动群发邮件任务
curl -X POST "http://localhost:8000/tasks/start/bulk_mail" \
  -H "Content-Type: application/json" \
  -d '{
    "template": {
      "subject": "欢迎 {recipient}！",
      "content": "你好 {recipient}，欢迎加入我们！"
    },
    "recipients": ["<EMAIL>", "<EMAIL>"],
    "account_pool": ["<EMAIL>", "<EMAIL>"]
  }'
```

#### 任务查询
```bash
# 查询任务状态
curl "http://localhost:8000/tasks/123"

# 查询所有任务
curl "http://localhost:8000/tasks"

# 按条件筛选
curl "http://localhost:8000/tasks?kind=reg&state=running"
```

### Provider配置

#### 验证码服务
支持的Provider：
- `manual`: 手动处理
- `2captcha`: 2captcha.com服务
- `anticaptcha`: anti-captcha.com服务

#### 短信服务
支持的Provider：
- `5sim`: 5sim.net服务
- `sms_activate`: sms-activate.org服务

#### 代理服务
支持的Provider：
- `custom`: 自定义代理列表
- `webshare`: webshare.io服务

### 命令行工具

```bash
# 启动Web服务器
python start.py server --host 0.0.0.0 --port 8080

# 启动后台工作进程
python start.py worker

# 健康检查
python start.py health

# 初始化应用
python start.py init

# 创建配置文件
python start.py config
```

## 🧪 测试

### 运行测试

```bash
# 运行所有测试
python run_tests.py

# 运行特定类型测试
python run_tests.py --type unit
python run_tests.py --type integration
python run_tests.py --type contract

# 生成覆盖率报告
python run_tests.py --coverage

# 运行代码检查
python run_tests.py --lint --format
```

### 使用pytest

```bash
# 安装测试依赖
pip install -r requirements-dev.txt

# 运行测试
pytest tests/ -v

# 运行特定标记的测试
pytest tests/ -m "unit" -v
pytest tests/ -m "integration" -v
pytest tests/ -m "contract" -v

# 生成覆盖率报告
pytest tests/ --cov=backend --cov-report=html
```

## 📖 开发指南

### 添加新Provider

1. 在 `backend/app/providers/implementations.py` 中实现Provider接口
2. 在 `backend/app/providers/factory.py` 中注册Provider
3. 添加相应的契约测试
4. 更新配置文档

### 添加新服务

1. 在 `backend/app/services/` 中创建服务类
2. 实现 `run(session, task)` 方法
3. 在 `main.py` 中注册服务到TaskEngine
4. 添加API端点和测试

### 代码风格

项目使用以下工具确保代码质量：
- **Ruff**: 代码检查和格式化
- **Black**: 代码格式化
- **MyPy**: 类型检查
- **pytest**: 测试框架

## 🔧 构建与部署

### 本地构建

```bash
# 使用PyInstaller构建
python build.py --tool pyinstaller --onefile

# 使用其他工具
python build.py --tool cx_freeze
python build.py --tool py2exe  # Windows only

# 清理构建文件
python build.py --clean
```

### Docker部署

```bash
# 构建镜像
docker build -t auto-em-v2 .

# 运行容器
docker run -d -p 8000:8000 \
  -e DATABASE_URL=****************************** \
  auto-em-v2
```

### 生产部署

```bash
# 使用Gunicorn + Uvicorn
gunicorn backend.main:app -w 4 -k uvicorn.workers.UvicornWorker

# 使用systemd服务
sudo cp auto-em-v2.service /etc/systemd/system/
sudo systemctl enable auto-em-v2
sudo systemctl start auto-em-v2
```

## 📊 监控与维护

### 日志管理

日志文件位置：`logs/auto-em-v2.log`

日志级别配置：
```env
LOG_LEVEL=INFO
LOG_FORMAT=json
```

### 健康检查

```bash
# 检查应用状态
curl http://localhost:8000/health

# 详细健康检查
python start.py health
```

### 数据库维护

```bash
# 数据库迁移
alembic upgrade head

# 备份数据库
pg_dump auto_em_v2 > backup.sql

# 清理旧任务
# 通过API或直接SQL清理
```

## 🤝 贡献指南

1. Fork 项目
2. 创建特性分支 (`git checkout -b feature/amazing-feature`)
3. 提交更改 (`git commit -m 'Add amazing feature'`)
4. 推送到分支 (`git push origin feature/amazing-feature`)
5. 打开 Pull Request

### 开发环境设置

```bash
# 安装开发依赖
poetry install --with dev

# 设置pre-commit hooks
pre-commit install

# 运行开发服务器
python start.py server --reload
```

## 📄 许可证

本项目采用 MIT 许可证。详情请参阅 [LICENSE](LICENSE) 文件。

## 📞 支持与反馈

- **问题报告**: [GitHub Issues](https://github.com/your-org/auto-em-v2/issues)
- **功能建议**: [GitHub Discussions](https://github.com/your-org/auto-em-v2/discussions)
- **技术文档**: [Wiki](https://github.com/your-org/auto-em-v2/wiki)

## 🔄 更新日志

### v2.0.0 (2024-08-29)
- 🎉 完全重构的架构
- ✨ 新增养护服务
- ✨ 新增群发邮件功能
- 🔧 改进的Provider系统
- 📱 RESTful API设计
- 🧪 全面的测试覆盖
- 📚 完善的文档

---

**Auto EM v2** - 让邮箱管理更简单、更智能！