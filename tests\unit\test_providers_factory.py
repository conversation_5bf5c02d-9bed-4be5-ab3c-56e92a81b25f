"""
Provider Factory 单元测试
"""
import pytest

from backend.app.providers.factory import (
    create_captcha_provider,
    create_proxy_provider,
    create_sms_provider,
)
from backend.app.providers.implementations import (
    CustomProxyProvider,
    SmsActivateProvider,
    TwoCaptchaProvider,
)


class TestProviderFactory:
    """Provider Factory 测试"""

    def test_create_captcha_provider_2captcha(self):
        """测试创建2captcha提供商"""
        config = {"api_key": "test_key"}
        provider = create_captcha_provider("2captcha", config)
        
        assert isinstance(provider, TwoCaptchaProvider)
        assert provider.api_key == "test_key"

    def test_create_captcha_provider_twocaptcha(self):
        """测试创建twocaptcha提供商（别名）"""
        config = {"api_key": "test_key"}
        provider = create_captcha_provider("twocaptcha", config)
        
        assert isinstance(provider, TwoCaptchaProvider)
        assert provider.api_key == "test_key"

    def test_create_captcha_provider_unknown(self):
        """测试创建未知验证码提供商"""
        config = {"api_key": "test_key"}
        
        with pytest.raises(ValueError, match="Unknown captcha provider: unknown"):
            create_captcha_provider("unknown", config)

    def test_create_sms_provider_sms_activate(self):
        """测试创建sms-activate提供商"""
        config = {"api_key": "test_key"}
        provider = create_sms_provider("sms-activate", config)
        
        assert isinstance(provider, SmsActivateProvider)
        assert provider.api_key == "test_key"

    def test_create_sms_provider_sms_activate_underscore(self):
        """测试创建sms_activate提供商（下划线版本）"""
        config = {"api_key": "test_key"}
        provider = create_sms_provider("sms_activate", config)
        
        assert isinstance(provider, SmsActivateProvider)
        assert provider.api_key == "test_key"

    def test_create_sms_provider_smsactivate(self):
        """测试创建smsactivate提供商（无分隔符版本）"""
        config = {"api_key": "test_key"}
        provider = create_sms_provider("smsactivate", config)
        
        assert isinstance(provider, SmsActivateProvider)
        assert provider.api_key == "test_key"

    def test_create_sms_provider_unknown(self):
        """测试创建未知短信提供商"""
        config = {"api_key": "test_key"}
        
        with pytest.raises(ValueError, match="Unknown sms provider: unknown"):
            create_sms_provider("unknown", config)

    def test_create_proxy_provider_custom(self):
        """测试创建custom代理提供商"""
        config = {"proxies": "proxy1:8080,proxy2:8080"}
        provider = create_proxy_provider("custom", config)
        
        assert isinstance(provider, CustomProxyProvider)
        assert len(provider.pool) == 2

    def test_create_proxy_provider_csv(self):
        """测试创建csv代理提供商（别名）"""
        config = {"proxies": "proxy1:8080"}
        provider = create_proxy_provider("csv", config)
        
        assert isinstance(provider, CustomProxyProvider)
        assert len(provider.pool) == 1

    def test_create_proxy_provider_list(self):
        """测试创建list代理提供商（别名）"""
        config = {"proxies": ""}
        provider = create_proxy_provider("list", config)
        
        assert isinstance(provider, CustomProxyProvider)
        assert len(provider.pool) == 0

    def test_create_proxy_provider_unknown(self):
        """测试创建未知代理提供商"""
        config = {"proxies": ""}
        
        with pytest.raises(ValueError, match="Unknown proxy provider: unknown"):
            create_proxy_provider("unknown", config)

    def test_create_providers_with_empty_config(self):
        """测试使用空配置创建提供商"""
        # 验证码提供商
        provider = create_captcha_provider("2captcha", {})
        assert isinstance(provider, TwoCaptchaProvider)
        assert provider.api_key == ""
        
        # 短信提供商
        provider = create_sms_provider("sms-activate", {})
        assert isinstance(provider, SmsActivateProvider)
        assert provider.api_key == ""
        
        # 代理提供商
        provider = create_proxy_provider("custom", {})
        assert isinstance(provider, CustomProxyProvider)
        assert len(provider.pool) == 0
