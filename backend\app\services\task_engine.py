from __future__ import annotations

import json
import threading
from collections.abc import Callable
from concurrent.futures import Future, ThreadPoolExecutor
from dataclasses import dataclass

from sqlmodel import Session

from backend.app.db.models import Task
from backend.app.repositories.task_repository import TaskRepository

TaskRunner = Callable[[Session, Task], None]


@dataclass
class EngineConfig:
    max_workers: int = 4


class TaskEngine:
    def __init__(self, session_factory: Callable[[], Session], config: EngineConfig | None = None) -> None:
        self._session_factory = session_factory
        self._config = config or EngineConfig()
        self._executor = ThreadPoolExecutor(max_workers=self._config.max_workers, thread_name_prefix="task-engine")
        self._lock = threading.Lock()
        self._running: dict[int, Future] = {}
        self._runners: dict[str, TaskRunner] = {}

    def register_runner(self, kind: str, runner: TaskRunner) -> None:
        self._runners[kind] = runner

    def start(self, kind: str, payload: dict | None = None) -> Task:
        with self._session_factory() as session:
            repo = TaskRepository(session)
            payload_json = json.dumps(payload) if payload else None
            task = repo.create(kind=kind, payload_json=payload_json)
            repo.update_state(task.id, state="running", step="queued")

        def _work(tid: int) -> None:
            with self._session_factory() as session:
                repo = TaskRepository(session)
                task_obj = repo.get(tid)
                if not task_obj:
                    return
                try:
                    runner = self._runners.get(task_obj.kind)
                    if not runner:
                        repo.update_state(tid, state="failed", result_json=json.dumps({"error": "runner_not_found"}))
                        return
                    repo.update_state(tid, state="running", step="running")
                    runner(session, task_obj)
                    repo.update_state(tid, state="succeeded", step="done")
                except Exception as e:
                    repo.update_state(tid, state="failed", result_json=json.dumps({"error": str(e)}))

        future = self._executor.submit(_work, task.id)
        with self._lock:
            self._running[task.id] = future
        return task

    def get(self, task_id: int) -> Task | None:
        with self._session_factory() as session:
            repo = TaskRepository(session)
            return repo.get(task_id)

    def list(self, kind: str | None = None, state: str | None = None):
        with self._session_factory() as session:
            repo = TaskRepository(session)
            return repo.list(kind=kind, state=state)

    def stop(self, task_id: int) -> bool:
        with self._lock:
            fut = self._running.get(task_id)
        # Cooperative cancel: mark state and rely on runner to check; hard cancel not guaranteed in threads
        with self._session_factory() as session:
            repo = TaskRepository(session)
            try:
                repo.update_state(task_id, state="canceled", step="stopping")
            finally:
                pass
        if fut:
            return True
        return False

    # Placeholders for pause/resume
    def pause(self, task_id: int) -> bool:
        with self._session_factory() as session:
            repo = TaskRepository(session)
            repo.update_state(task_id, state="paused", step="paused")
        return True

    def resume(self, task_id: int) -> bool:
        with self._session_factory() as session:
            repo = TaskRepository(session)
            task = repo.get(task_id)
            if not task:
                return False
            # For MVP: simply mark running; real resume should continue from snapshot
            repo.update_state(task_id, state="running", step="resumed")
        return True
