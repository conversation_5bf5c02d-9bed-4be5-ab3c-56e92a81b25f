#!/usr/bin/env python3
"""
启动脚本 - Auto EM v2应用的统一入口
"""
import os
import sys

# 添加backend目录到Python路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), "backend"))

if __name__ == "__main__":
    try:
        from backend.cli import main

        main()
    except ImportError as e:
        print(f"[ERROR] Import error: {e}")
        print("Please install dependencies: pip install -r requirements.txt")
        sys.exit(1)
    except Exception as e:
        print(f"[ERROR] Startup error: {e}")
        sys.exit(1)
