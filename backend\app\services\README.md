# Services 模块

## 概述

Services 模块包含 Auto EM v2 的核心业务逻辑服务，负责处理邮箱注册、账户养护、群发邮件等主要功能。

## 模块职责

### 核心原则
- **单一职责**: 每个服务专注于特定的业务领域
- **松耦合**: 服务间通过明确的接口交互，减少直接依赖
- **可测试**: 服务设计支持单元测试和集成测试
- **可扩展**: 易于添加新功能和修改现有逻辑

### 依赖关系
- **数据层**: 依赖 `repositories` 进行数据访问
- **Provider层**: 依赖 `providers` 获取外部服务
- **核心层**: 依赖 `core` 获取配置和日志
- **任务引擎**: 集成到 `TaskEngine` 进行异步执行

## 服务列表

### 1. TaskEngine (`task_engine.py`)
**职责**: 任务调度和生命周期管理

**功能**:
- 任务创建和状态管理
- 异步任务执行调度
- 服务注册和路由
- 任务查询和筛选

**核心方法**:
```python
def start(kind: str, payload: dict) -> Task
def get(task_id: int) -> Optional[Task]
def list(kind: str = None, state: str = None) -> List[Task]
def register_runner(kind: str, runner_func)
```

**依赖**:
- `TaskRepository`: 任务数据持久化
- 各业务服务的 `run` 方法

### 2. RegistrationPipelineService (`registration_pipeline.py`)
**职责**: 邮箱注册流程管理

**功能**:
- 多步骤注册流程编排
- 代理选择和管理
- 验证码和短信处理集成
- 注册状态跟踪和错误处理

**核心流程**:
1. `select_proxy` - 代理选择
2. `fetch_signup_form` - 获取注册表单
3. `submit_registration` - 提交注册信息
4. `solve_captcha_if_needed` - 验证码处理
5. `verify_phone_if_needed` - 短信验证
6. `enable_imap_pop3` - 启用邮箱协议
7. `fetch_oauth_token` - 获取认证令牌

**依赖**:
- `TaskRepository`: 任务状态更新
- `ProxyProvider`: 代理服务
- `CaptchaProvider`: 验证码服务 (未来)
- `SmsProvider`: 短信服务 (未来)

### 3. NurtureService (`nurture_service.py`)
**职责**: 已注册账户的日常养护

**功能**:
- 周期性登录检查
- 邮件标记已读
- 账户资料更新
- 综合维护任务

**养护类型**:
- `login_check`: 登录状态验证
- `mark_emails_read`: 批量标记邮件已读
- `update_profile`: 更新头像、昵称、签名
- `maintenance`: 执行综合维护任务

**依赖**:
- `TaskRepository`: 任务管理
- `AccountRepository`: 账户数据
- `ProxyProvider`: 网络代理
- 外部邮件服务API (实际实现中)

### 4. BulkMailService (`bulk_mail_service.py`)
**职责**: 群发邮件管理

**功能**:
- 模板化邮件内容生成
- 个性化变量替换
- 轻随机化处理
- 发送频率控制
- 账号池轮询
- 实时统计和监控

**核心特性**:
- **模板系统**: 支持 `{recipient}`, `{sequence}`, `{date}`, `{time}` 变量
- **限速策略**: 可配置的发送间隔和日/小时限制
- **随机化**: 同义词替换和延迟随机化
- **容错机制**: 失败重试和错误统计

**依赖**:
- `TaskRepository`: 任务状态管理
- `AccountRepository`: 发件账户管理
- 邮件发送服务 (实际实现中)

### 5. ProfileManager (`profile_manager.py`)
**职责**: 账户资料文件管理

**功能**:
- 头像文件管理
- 昵称库维护
- 签名库管理
- 临时文件清理
- 资料库统计

**文件结构**:
```
assets/
├── avatars/          # 头像文件
├── nicknames/        # 昵称文本文件
├── signatures/       # 签名文本文件
└── temp/            # 临时文件
```

**依赖**:
- 文件系统访问
- 无数据库依赖

## 使用模式

### 1. 通过TaskEngine使用服务
```python
# 在 main.py 中注册服务
engine = TaskEngine(session_factory=session_factory)
engine.register_runner("reg", RegistrationPipelineService().run)
engine.register_runner("nurture", NurtureService().run)
engine.register_runner("bulk_mail", BulkMailService().run)

# 启动任务
task = engine.start("reg", {"email": "<EMAIL>"})
```

### 2. 直接实例化服务
```python
# 用于测试或特殊场景
service = RegistrationPipelineService()
service.run(session, task)
```

## 扩展指南

### 添加新服务

1. **创建服务类**:
```python
class NewService:
    def run(self, session: Session, task: Task) -> None:
        # 业务逻辑实现
        pass
```

2. **注册服务**:
```python
# 在 main.py 中
engine.register_runner("new_service", NewService().run)
```

3. **添加API端点**:
```python
@app.post("/tasks/start/new_service")
def start_new_service_task(payload: dict):
    task = engine_instance.start("new_service", payload)
    return {"id": task.id, "state": task.state}
```

### 服务设计原则

1. **统一接口**: 实现 `run(session, task)` 方法
2. **错误处理**: 使用 `_fail_task` 模式处理错误
3. **状态更新**: 及时更新任务状态和步骤
4. **日志记录**: 使用 `log_event` 记录关键操作
5. **资源清理**: 确保资源正确释放

## 测试策略

### 单元测试
- Mock外部依赖 (Repository, Provider)
- 测试业务逻辑分支
- 验证错误处理机制

### 集成测试
- 使用真实数据库
- 测试完整工作流程
- 验证服务间协作

### 契约测试
- 验证Provider接口兼容性
- 测试外部服务集成
- 确保API响应格式

## 性能考虑

### 异步处理
- 使用TaskEngine进行异步任务调度
- 避免长时间阻塞操作
- 实现适当的超时机制

### 资源管理
- 合理控制并发任务数量
- 及时释放数据库连接
- 清理临时文件和缓存

### 监控指标
- 任务执行时间
- 成功/失败率
- 资源使用情况

## 故障排除

### 常见问题

1. **任务卡在pending状态**
   - 检查TaskEngine是否正确注册服务
   - 验证session_factory是否正常工作

2. **Provider连接失败**
   - 检查网络连接和配置
   - 验证API密钥和权限

3. **数据库连接问题**
   - 确认数据库服务状态
   - 检查连接字符串配置

### 调试技巧

1. **启用详细日志**:
```python
import logging
logging.getLogger('backend.app.services').setLevel(logging.DEBUG)
```

2. **使用调试断点**:
```python
import pdb; pdb.set_trace()
```

3. **监控任务状态**:
```python
# 通过API查询任务状态
curl http://localhost:8000/tasks/123
```

## 配置参数

### 环境变量
- `LOG_LEVEL`: 日志级别
- `DATABASE_URL`: 数据库连接
- `CAPTCHA_PROVIDER`: 验证码服务商
- `SMS_PROVIDER`: 短信服务商
- `PROXY_PROVIDER`: 代理服务商

### 服务配置
每个服务支持通过任务payload传递配置参数，具体参数请参考各服务的实现文档。

---

## 更新日志

### v2.0.0
- 初始化Services模块架构
- 实现核心业务服务
- 建立统一的任务处理模式
- 添加完整的错误处理机制

---

Services模块是Auto EM v2的业务核心，负责实现所有主要功能逻辑。通过清晰的职责分离和标准化的接口设计，确保系统的可维护性和扩展性。