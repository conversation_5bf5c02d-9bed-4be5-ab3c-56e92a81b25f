"""
Provider契约测试 - 确保所有Provider实现符合接口规范
"""

import pytest

from backend.app.providers.base import CaptchaProvider, ProxyProvider, SmsProvider
from backend.app.providers.factory import create_captcha_provider, create_proxy_provider, create_sms_provider


class TestCaptchaProviderContract:
    """测试CaptchaProvider接口契约"""

    @pytest.fixture(params=["manual", "2captcha", "anticaptcha"])
    def captcha_provider(self, request):
        """参数化fixture，测试所有captcha provider实现"""
        provider_name = request.param
        config = {}

        # 根据不同provider设置相应的测试配置
        if provider_name == "2captcha":
            config = {"api_key": "test_key"}
        elif provider_name == "anticaptcha":
            config = {"api_key": "test_key"}

        try:
            return create_captcha_provider(provider_name, config)
        except Exception:
            pytest.skip(f"Provider {provider_name} not available for testing")

    def test_implements_interface(self, captcha_provider):
        """确保provider实现了CaptchaProvider接口"""
        assert isinstance(captcha_provider, CaptchaProvider)

    def test_solve_method_exists(self, captcha_provider):
        """确保solve方法存在且可调用"""
        assert hasattr(captcha_provider, "solve")
        assert callable(captcha_provider.solve)

    def test_test_connectivity_method_exists(self, captcha_provider):
        """确保test_connectivity方法存在且可调用"""
        assert hasattr(captcha_provider, "test_connectivity")
        assert callable(captcha_provider.test_connectivity)

    def test_test_connectivity_returns_dict(self, captcha_provider):
        """确保test_connectivity返回dict"""
        result = captcha_provider.test_connectivity()
        assert isinstance(result, dict)
        # 应该包含基本的诊断信息
        assert "ok" in result

    @pytest.mark.network
    def test_solve_with_image_base64(self, captcha_provider):
        """测试使用base64图像求解验证码（需要网络）"""
        # 使用一个简单的测试图像（1x1像素白色图片的base64）
        test_image = "iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mP8/5+hHgAHggJ/PchI7wAAAABJRU5ErkJggg=="

        try:
            result = captcha_provider.solve(image_base64=test_image)
            # 即使失败也应该返回字符串或抛出异常
            assert isinstance(result, str) or result is None
        except Exception as e:
            # 允许抛出异常，但应该是有意义的异常
            assert str(e)  # 确保异常消息不为空


class TestSmsProviderContract:
    """测试SmsProvider接口契约"""

    @pytest.fixture(params=["5sim", "sms_activate"])
    def sms_provider(self, request):
        """参数化fixture，测试所有sms provider实现"""
        provider_name = request.param
        config = {"api_key": "test_key"}

        try:
            return create_sms_provider(provider_name, config)
        except Exception:
            pytest.skip(f"Provider {provider_name} not available for testing")

    def test_implements_interface(self, sms_provider):
        """确保provider实现了SmsProvider接口"""
        assert isinstance(sms_provider, SmsProvider)

    def test_get_number_method_exists(self, sms_provider):
        """确保get_number方法存在且可调用"""
        assert hasattr(sms_provider, "get_number")
        assert callable(sms_provider.get_number)

    def test_poll_code_method_exists(self, sms_provider):
        """确保poll_code方法存在且可调用"""
        assert hasattr(sms_provider, "poll_code")
        assert callable(sms_provider.poll_code)

    def test_set_status_method_exists(self, sms_provider):
        """确保set_status方法存在且可调用"""
        assert hasattr(sms_provider, "set_status")
        assert callable(sms_provider.set_status)

    def test_test_connectivity_method_exists(self, sms_provider):
        """确保test_connectivity方法存在且可调用"""
        assert hasattr(sms_provider, "test_connectivity")
        assert callable(sms_provider.test_connectivity)

    def test_test_connectivity_returns_dict(self, sms_provider):
        """确保test_connectivity返回dict"""
        result = sms_provider.test_connectivity()
        assert isinstance(result, dict)
        assert "ok" in result

    @pytest.mark.network
    def test_get_number_returns_dict(self, sms_provider):
        """测试get_number返回字典格式（需要网络）"""
        try:
            result = sms_provider.get_number("microsoft")
            if result:  # 可能没有可用号码
                assert isinstance(result, dict)
                assert "activation_id" in result
                assert "phone_number" in result
        except Exception as e:
            # 允许抛出异常（如余额不足等）
            assert str(e)


class TestProxyProviderContract:
    """测试ProxyProvider接口契约"""

    @pytest.fixture(params=["custom", "webshare"])
    def proxy_provider(self, request):
        """参数化fixture，测试所有proxy provider实现"""
        provider_name = request.param
        config = {}

        if provider_name == "custom":
            config = {"proxies": "http://proxy1:8080,http://proxy2:8080"}
        elif provider_name == "webshare":
            config = {"api_key": "test_key"}

        try:
            return create_proxy_provider(provider_name, config)
        except Exception:
            pytest.skip(f"Provider {provider_name} not available for testing")

    def test_implements_interface(self, proxy_provider):
        """确保provider实现了ProxyProvider接口"""
        assert isinstance(proxy_provider, ProxyProvider)

    def test_next_method_exists(self, proxy_provider):
        """确保next方法存在且可调用"""
        assert hasattr(proxy_provider, "next")
        assert callable(proxy_provider.next)

    def test_test_connectivity_method_exists(self, proxy_provider):
        """确保test_connectivity方法存在且可调用"""
        assert hasattr(proxy_provider, "test_connectivity")
        assert callable(proxy_provider.test_connectivity)

    def test_test_connectivity_returns_dict(self, proxy_provider):
        """确保test_connectivity返回dict"""
        result = proxy_provider.test_connectivity()
        assert isinstance(result, dict)
        assert "ok" in result

    def test_next_returns_optional_string(self, proxy_provider):
        """测试next方法返回Optional[str]"""
        result = proxy_provider.next()
        assert result is None or isinstance(result, str)

        # 如果返回字符串，应该是有效的URL格式
        if result:
            assert result.startswith(("http://", "https://"))


class TestProviderFactory:
    """测试Provider工厂方法"""

    def test_create_captcha_provider_with_invalid_name(self):
        """测试使用无效名称创建captcha provider"""
        with pytest.raises(ValueError, match="Unsupported captcha provider"):
            create_captcha_provider("invalid_provider", {})

    def test_create_sms_provider_with_invalid_name(self):
        """测试使用无效名称创建sms provider"""
        with pytest.raises(ValueError, match="Unsupported SMS provider"):
            create_sms_provider("invalid_provider", {})

    def test_create_proxy_provider_with_invalid_name(self):
        """测试使用无效名称创建proxy provider"""
        with pytest.raises(ValueError, match="Unsupported proxy provider"):
            create_proxy_provider("invalid_provider", {})

    def test_create_providers_with_empty_config(self):
        """测试使用空配置创建providers"""
        # 这些应该能正常工作或抛出明确的错误信息
        try:
            captcha_provider = create_captcha_provider("manual", {})
            assert isinstance(captcha_provider, CaptchaProvider)
        except Exception as e:
            assert str(e)  # 应该有明确的错误信息

        try:
            proxy_provider = create_proxy_provider("custom", {})
            assert isinstance(proxy_provider, ProxyProvider)
        except Exception as e:
            assert str(e)
