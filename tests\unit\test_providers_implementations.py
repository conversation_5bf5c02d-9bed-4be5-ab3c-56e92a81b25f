"""
Provider Implementations 单元测试
"""
import pytest
from unittest.mock import Mock, patch

from backend.app.providers.implementations import (
    CustomProxyProvider,
    SmsActivateProvider,
    TwoCaptchaProvider,
)


class TestTwoCaptchaProvider:
    """TwoCaptchaProvider 测试"""

    def test_init(self):
        """测试初始化"""
        provider = TwoCaptchaProvider("test_key")
        assert provider.api_key == "test_key"

    def test_solve_without_api_key(self):
        """测试没有API密钥时的solve方法"""
        provider = TwoCaptchaProvider("")
        
        with pytest.raises(ValueError, match="2captcha api_key missing"):
            provider.solve()

    def test_solve_with_api_key(self):
        """测试有API密钥时的solve方法"""
        provider = TwoCaptchaProvider("test_key")
        result = provider.solve()
        
        assert result == "PLACEHOLDER_SOLUTION"

    def test_solve_with_parameters(self):
        """测试带参数的solve方法"""
        provider = TwoCaptchaProvider("test_key")
        result = provider.solve(
            image_base64="base64_data",
            site_key="site_key",
            context={"url": "https://example.com"}
        )
        
        assert result == "PLACEHOLDER_SOLUTION"

    @patch('backend.app.providers.implementations.requests.get')
    def test_test_connectivity_success(self, mock_get):
        """测试连接性检查成功"""
        mock_response = Mock()
        mock_response.status_code = 200
        mock_response.json.return_value = {"status": 1, "request": "10.50"}
        mock_get.return_value = mock_response

        provider = TwoCaptchaProvider("test_key")
        result = provider.test_connectivity()

        assert result["ok"] is True
        assert result["balance"] == "10.50"

    @patch('backend.app.providers.implementations.requests.get')
    def test_test_connectivity_failure(self, mock_get):
        """测试连接性检查失败"""
        mock_get.side_effect = Exception("Network error")
        
        provider = TwoCaptchaProvider("test_key")
        result = provider.test_connectivity()
        
        assert result["ok"] is False
        assert "error" in result


class TestSmsActivateProvider:
    """SmsActivateProvider 测试"""

    def test_init(self):
        """测试初始化"""
        provider = SmsActivateProvider("test_key")
        assert provider.api_key == "test_key"

    def test_get_number_not_implemented(self):
        """测试get_number方法未实现"""
        provider = SmsActivateProvider("test_key")
        
        with pytest.raises(NotImplementedError):
            provider.get_number("service")

    def test_poll_code_not_implemented(self):
        """测试poll_code方法未实现"""
        provider = SmsActivateProvider("test_key")
        
        with pytest.raises(NotImplementedError):
            provider.poll_code("activation_id")

    def test_set_status_not_implemented(self):
        """测试set_status方法未实现"""
        provider = SmsActivateProvider("test_key")
        
        with pytest.raises(NotImplementedError):
            provider.set_status("activation_id", "status")

    @patch('backend.app.providers.implementations.requests.get')
    def test_test_connectivity_success(self, mock_get):
        """测试连接性检查成功"""
        mock_response = Mock()
        mock_response.status_code = 200
        mock_response.text = "ACCESS_BALANCE:15.50"
        mock_get.return_value = mock_response

        provider = SmsActivateProvider("test_key")
        result = provider.test_connectivity()

        assert result["ok"] is True
        assert result["balance"] == "15.50"

    @patch('backend.app.providers.implementations.requests.get')
    def test_test_connectivity_failure(self, mock_get):
        """测试连接性检查失败"""
        mock_get.side_effect = Exception("Network error")
        
        provider = SmsActivateProvider("test_key")
        result = provider.test_connectivity()
        
        assert result["ok"] is False
        assert "error" in result


class TestCustomProxyProvider:
    """CustomProxyProvider 测试"""

    def test_init_empty(self):
        """测试空代理列表初始化"""
        provider = CustomProxyProvider("")
        assert provider.pool == []

    def test_init_single_proxy(self):
        """测试单个代理初始化"""
        provider = CustomProxyProvider("proxy1:8080")
        assert provider.pool == ["proxy1:8080"]

    def test_init_multiple_proxies(self):
        """测试多个代理初始化"""
        provider = CustomProxyProvider("proxy1:8080,proxy2:8080,proxy3:8080")
        assert len(provider.pool) == 3
        assert "proxy1:8080" in provider.pool
        assert "proxy2:8080" in provider.pool
        assert "proxy3:8080" in provider.pool

    def test_init_with_whitespace(self):
        """测试带空白字符的代理列表"""
        provider = CustomProxyProvider(" proxy1:8080 , proxy2:8080 , ")
        assert len(provider.pool) == 2
        assert "proxy1:8080" in provider.pool
        assert "proxy2:8080" in provider.pool

    def test_next_empty_pool(self):
        """测试空代理池的next方法"""
        provider = CustomProxyProvider("")
        result = provider.next()
        assert result is None

    def test_next_with_proxies(self):
        """测试有代理时的next方法"""
        provider = CustomProxyProvider("proxy1:8080,proxy2:8080")
        result = provider.next()
        assert result in ["proxy1:8080", "proxy2:8080"]

    def test_test_connectivity_empty_pool(self):
        """测试空代理池的连接性检查"""
        provider = CustomProxyProvider("")
        result = provider.test_connectivity()
        
        assert result["ok"] is False
        assert result["size"] == 0

    def test_test_connectivity_with_proxies(self):
        """测试有代理时的连接性检查"""
        provider = CustomProxyProvider("proxy1:8080,proxy2:8080")
        result = provider.test_connectivity()
        
        assert result["ok"] is True
        assert result["size"] == 2
