name: CI

on:
  push:
    branches: [ main, develop ]
  pull_request:
    branches: [ main, develop ]

jobs:
  test:
    runs-on: ubuntu-latest
    strategy:
      matrix:
        python-version: ["3.11", "3.12"]

    services:
      postgres:
        image: postgres:13
        env:
          POSTGRES_PASSWORD: postgres
          POSTGRES_USER: postgres
          POSTGRES_DB: test_auto_em_v2
        ports:
          - 5432:5432
        options: >-
          --health-cmd pg_isready
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5

    steps:
    - uses: actions/checkout@v4

    - name: Set up Python ${{ matrix.python-version }}
      uses: actions/setup-python@v4
      with:
        python-version: ${{ matrix.python-version }}

    - name: Install system dependencies
      run: |
        sudo apt-get update
        sudo apt-get install -y libpq-dev

    - name: Cache pip packages
      uses: actions/cache@v3
      with:
        path: ~/.cache/pip
        key: ${{ runner.os }}-pip-${{ hashFiles('requirements-dev.txt') }}
        restore-keys: |
          ${{ runner.os }}-pip-

    - name: Install Python dependencies
      run: |
        python -m pip install --upgrade pip
        pip install -r requirements-dev.txt
        # Install main dependencies if requirements.txt exists
        if [ -f requirements.txt ]; then pip install -r requirements.txt; fi
        # Install additional dependencies for the project
        pip install fastapi uvicorn sqlmodel psycopg2-binary requests

    - name: Set up environment variables
      run: |
        echo "DATABASE_URL=postgresql://postgres:postgres@localhost:5432/test_auto_em_v2" >> $GITHUB_ENV
        echo "ENVIRONMENT=test" >> $GITHUB_ENV

    - name: Run linting with Ruff
      run: |
        ruff check backend/ tests/ --output-format=github

    - name: Run formatting check with Black
      run: |
        black --check backend/ tests/

    - name: Run type checking with MyPy
      run: |
        mypy backend/ --ignore-missing-imports --no-strict-optional
      continue-on-error: true  # MyPy failures won't fail the build initially

    - name: Run unit tests
      run: |
        pytest tests/unit/ -v --cov=backend --cov-report=xml --cov-report=term-missing -m "unit"

    - name: Run contract tests
      run: |
        pytest tests/contract/ -v -m "contract"

    - name: Run integration tests
      run: |
        pytest tests/integration/ -v -m "integration"
      env:
        DATABASE_URL: postgresql://postgres:postgres@localhost:5432/test_auto_em_v2

    - name: Run all tests with coverage
      run: |
        pytest tests/ -v --cov=backend --cov-report=xml --cov-report=term-missing --cov-fail-under=80

    - name: Upload coverage reports to Codecov
      uses: codecov/codecov-action@v3
      with:
        file: ./coverage.xml
        flags: unittests
        name: codecov-umbrella
      env:
        CODECOV_TOKEN: ${{ secrets.CODECOV_TOKEN }}

  security-scan:
    runs-on: ubuntu-latest
    steps:
    - uses: actions/checkout@v4

    - name: Set up Python
      uses: actions/setup-python@v4
      with:
        python-version: "3.11"

    - name: Install security scanning tools
      run: |
        python -m pip install --upgrade pip
        pip install bandit safety

    - name: Run Bandit security scan
      run: |
        bandit -r backend/ -f json -o bandit-report.json || true
        bandit -r backend/ -f txt

    - name: Run Safety security scan
      run: |
        safety check --json --output safety-report.json || true
        safety check

    - name: Upload security scan results
      uses: actions/upload-artifact@v3
      with:
        name: security-reports
        path: |
          bandit-report.json
          safety-report.json

  build-and-test-docker:
    runs-on: ubuntu-latest
    steps:
    - uses: actions/checkout@v4

    - name: Set up Docker Buildx
      uses: docker/setup-buildx-action@v3

    - name: Create Dockerfile for testing
      run: |
        cat > Dockerfile << 'EOF'
        FROM python:3.11-slim

        WORKDIR /app

        # Install system dependencies
        RUN apt-get update && apt-get install -y \
            libpq-dev gcc \
            && rm -rf /var/lib/apt/lists/*

        # Copy requirements first for better caching
        COPY requirements-dev.txt .
        RUN pip install --no-cache-dir -r requirements-dev.txt

        # Install main dependencies
        RUN pip install fastapi uvicorn sqlmodel psycopg2-binary requests

        # Copy application code
        COPY backend/ backend/
        COPY tests/ tests/
        COPY pytest.ini pyproject.toml ./

        # Run tests
        CMD ["pytest", "tests/", "-v"]
        EOF

    - name: Build Docker image
      run: |
        docker build -t auto-em-v2-test .

    - name: Run tests in Docker
      run: |
        docker run --rm auto-em-v2-test pytest tests/unit/ tests/contract/ -v -m "unit or contract"