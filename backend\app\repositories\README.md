# Repositories 模块

## 概述

Repositories 模块实现了Auto EM v2的数据访问层，采用Repository模式封装数据库操作，提供清晰的数据访问接口，将业务逻辑与数据持久化逻辑分离。

## 设计理念

### 核心原则
- **Repository模式**: 封装数据访问逻辑，提供领域对象的集合接口
- **单一职责**: 每个Repository负责单一实体的数据操作
- **数据库无关**: 通过SQLModel抽象，支持多种数据库
- **事务管理**: 支持事务操作和数据一致性
- **查询优化**: 提供高效的数据查询方法

### 架构优势
- **解耦**: 业务逻辑与数据存储分离
- **可测试**: 易于进行单元测试和集成测试
- **可维护**: 数据访问逻辑集中管理
- **可扩展**: 易于添加新的查询方法

## 模块结构

```
repositories/
├── task_repository.py           # 任务数据访问
├── account_repository.py        # 账户数据访问  
├── provider_config_repository.py # Provider配置访问
└── README.md                   # 本文档
```

## Repository规范

### 基础Repository接口
```python
class BaseRepository:
    def __init__(self, session: Session):
        self.session = session
    
    def create(self, entity) -> Entity:
        """创建新实体"""
        
    def get_by_id(self, id: int) -> Optional[Entity]:
        """根据ID获取实体"""
        
    def update(self, entity) -> Entity:
        """更新实体"""
        
    def delete(self, id: int) -> bool:
        """删除实体"""
        
    def list(self, **filters) -> List[Entity]:
        """查询实体列表"""
```

## Repository实现

### 1. TaskRepository (`task_repository.py`)
**职责**: 任务实体的数据访问和管理

**核心功能**:
- 任务的CRUD操作
- 任务状态更新
- 任务查询和过滤
- 批量操作支持

**主要方法**:
```python
class TaskRepository:
    def create_task(self, kind: str, payload_json: str) -> Task:
        """创建新任务"""
    
    def update_state(self, task_id: int, state: str, 
                    step: str = None, result_json: str = None) -> bool:
        """更新任务状态"""
    
    def get_by_id(self, task_id: int) -> Optional[Task]:
        """根据ID获取任务"""
    
    def list_tasks(self, kind: str = None, 
                  state: str = None, 
                  limit: int = 100) -> List[Task]:
        """查询任务列表"""
    
    def get_running_tasks(self) -> List[Task]:
        """获取运行中的任务"""
    
    def cleanup_old_tasks(self, days: int = 30) -> int:
        """清理旧任务"""
```

**使用示例**:
```python
def create_registration_task(session, email):
    repo = TaskRepository(session)
    payload = json.dumps({"email": email})
    task = repo.create_task("reg", payload)
    return task

def update_task_progress(session, task_id, step):
    repo = TaskRepository(session)
    repo.update_state(task_id, "running", step=step)
```

### 2. AccountRepository (`account_repository.py`)
**职责**: 账户实体的数据访问和管理

**核心功能**:
- 账户信息存储
- 账户状态管理
- 养护记录追踪
- 账户池查询

**主要方法**:
```python
class AccountRepository:
    def create_account(self, email: str, password: str = None, 
                      provider: str = None) -> Account:
        """创建账户记录"""
    
    def get_by_email(self, email: str) -> Optional[Account]:
        """根据邮箱获取账户"""
    
    def update_status(self, account_id: int, status: str) -> bool:
        """更新账户状态"""
    
    def get_active_accounts(self) -> List[Account]:
        """获取活跃账户"""
    
    def update_last_nurtured(self, account_id: int, 
                           timestamp: datetime) -> bool:
        """更新最后养护时间"""
    
    def get_accounts_for_nurture(self, hours_since: int = 24) -> List[Account]:
        """获取需要养护的账户"""
    
    def batch_update_status(self, account_ids: List[int], 
                          status: str) -> int:
        """批量更新状态"""
```

**账户状态定义**:
- `pending`: 注册中
- `active`: 可用状态
- `suspended`: 暂停使用
- `banned`: 被封禁
- `inactive`: 非活跃

**使用示例**:
```python
def register_new_account(session, email, password):
    repo = AccountRepository(session)
    account = repo.create_account(email, password, "outlook")
    return account

def get_available_senders(session):
    repo = AccountRepository(session)
    return repo.get_active_accounts()
```

### 3. ProviderConfigRepository (`provider_config_repository.py`)
**职责**: Provider配置的持久化管理

**核心功能**:
- Provider配置存储
- 配置版本管理
- 配置查询和切换
- 敏感信息加密

**主要方法**:
```python
class ProviderConfigRepository:
    def save_config(self, provider_type: str, provider_name: str, 
                   config_data: dict) -> ProviderConfig:
        """保存Provider配置"""
    
    def get_config(self, provider_type: str, 
                  provider_name: str) -> Optional[ProviderConfig]:
        """获取Provider配置"""
    
    def get_active_config(self, provider_type: str) -> Optional[ProviderConfig]:
        """获取活跃配置"""
    
    def list_configs(self, provider_type: str = None) -> List[ProviderConfig]:
        """列出配置"""
    
    def update_config(self, config_id: int, 
                     config_data: dict) -> bool:
        """更新配置"""
    
    def set_active(self, config_id: int) -> bool:
        """设置为活跃配置"""
```

**配置类型**:
- `captcha`: 验证码服务配置
- `sms`: 短信服务配置  
- `proxy`: 代理服务配置
- `email`: 邮件服务配置

## 数据模型

### Task模型
```python
class Task(SQLModel, table=True):
    id: Optional[int] = Field(primary_key=True)
    kind: str                    # 任务类型
    state: str = "pending"       # 任务状态
    step: str = "init"           # 当前步骤
    payload_json: Optional[str]  # 任务参数
    result_json: Optional[str]   # 执行结果
    created_at: datetime         # 创建时间
    updated_at: Optional[datetime] # 更新时间
```

### Account模型 (示例)
```python
class Account(SQLModel, table=True):
    id: Optional[int] = Field(primary_key=True)
    email: str = Field(unique=True, index=True)
    password_hash: Optional[str]
    provider: str                # 邮箱服务商
    state: str = "pending"       # 账户状态
    created_at: datetime
    last_nurtured_at: Optional[datetime]
    nurture_count: int = 0
```

### ProviderConfig模型 (示例)
```python
class ProviderConfig(SQLModel, table=True):
    id: Optional[int] = Field(primary_key=True)
    provider_type: str           # captcha, sms, proxy
    provider_name: str           # 具体Provider名称
    config_data_encrypted: str   # 加密的配置数据
    is_active: bool = False      # 是否为活跃配置
    created_at: datetime
    updated_at: Optional[datetime]
```

## 事务管理

### 基础事务操作
```python
def transfer_account_status(session, from_status, to_status):
    repo = AccountRepository(session)
    
    try:
        # 事务开始
        accounts = repo.list_by_status(from_status)
        for account in accounts:
            repo.update_status(account.id, to_status)
        
        session.commit()  # 提交事务
        return True
        
    except Exception as e:
        session.rollback()  # 回滚事务
        logger.error(f"Transaction failed: {e}")
        return False
```

### Repository级事务
```python
class TaskRepository:
    def create_task_with_account(self, task_data, account_data):
        """原子操作：创建任务并关联账户"""
        try:
            task = self.create_task(**task_data)
            
            account_repo = AccountRepository(self.session)
            account = account_repo.create_account(**account_data)
            
            # 建立关联
            task.account_id = account.id
            self.session.add(task)
            self.session.commit()
            
            return task, account
            
        except Exception:
            self.session.rollback()
            raise
```

## 查询优化

### 索引策略
```python
# 在模型定义中添加索引
class Task(SQLModel, table=True):
    id: Optional[int] = Field(primary_key=True)
    kind: str = Field(index=True)        # 任务类型索引
    state: str = Field(index=True)       # 状态索引  
    created_at: datetime = Field(index=True)  # 时间索引
```

### 批量操作
```python
class TaskRepository:
    def bulk_update_states(self, task_ids: List[int], new_state: str) -> int:
        """批量更新任务状态"""
        stmt = (
            update(Task)
            .where(Task.id.in_(task_ids))
            .values(state=new_state, updated_at=datetime.utcnow())
        )
        result = self.session.execute(stmt)
        self.session.commit()
        return result.rowcount
```

### 分页查询
```python
class TaskRepository:
    def list_tasks_paginated(self, page: int = 1, 
                           page_size: int = 50, **filters) -> dict:
        """分页查询任务"""
        offset = (page - 1) * page_size
        
        query = select(Task)
        
        # 应用过滤条件
        if filters.get('kind'):
            query = query.where(Task.kind == filters['kind'])
        if filters.get('state'):
            query = query.where(Task.state == filters['state'])
        
        # 获取总数
        count_query = select(func.count(Task.id)).select_from(query)
        total = self.session.execute(count_query).scalar()
        
        # 分页查询
        query = query.offset(offset).limit(page_size)
        tasks = self.session.execute(query).scalars().all()
        
        return {
            "tasks": tasks,
            "total": total,
            "page": page,
            "page_size": page_size,
            "pages": (total + page_size - 1) // page_size
        }
```

## 测试策略

### Repository测试基础
```python
@pytest.fixture
def test_session():
    engine = create_engine("sqlite:///:memory:")
    SQLModel.metadata.create_all(engine)
    with Session(engine) as session:
        yield session

def test_create_task(test_session):
    repo = TaskRepository(test_session)
    
    task = repo.create_task("reg", '{"email": "<EMAIL>"}')
    
    assert task.id is not None
    assert task.kind == "reg"
    assert task.state == "pending"
```

### Mock Repository
```python
class MockTaskRepository:
    def __init__(self):
        self.tasks = {}
        self.next_id = 1
    
    def create_task(self, kind: str, payload_json: str) -> Task:
        task = Task(
            id=self.next_id,
            kind=kind,
            payload_json=payload_json
        )
        self.tasks[self.next_id] = task
        self.next_id += 1
        return task
```

### 集成测试
```python
def test_task_repository_integration():
    with TestClient(app) as client:
        # 创建任务
        response = client.post("/tasks/start/reg", 
                              json={"email": "<EMAIL>"})
        task_id = response.json()["id"]
        
        # 验证数据库中的任务
        with next(get_session()) as session:
            repo = TaskRepository(session)
            task = repo.get_by_id(task_id)
            
            assert task is not None
            assert task.kind == "reg"
```

## 性能监控

### 查询性能监控
```python
import time
from functools import wraps

def monitor_query_time(func):
    @wraps(func)
    def wrapper(*args, **kwargs):
        start_time = time.time()
        result = func(*args, **kwargs)
        execution_time = time.time() - start_time
        
        logger.info(f"{func.__name__} executed in {execution_time:.3f}s")
        
        # 记录慢查询
        if execution_time > 1.0:
            logger.warning(f"Slow query detected: {func.__name__}")
        
        return result
    return wrapper

class TaskRepository:
    @monitor_query_time
    def list_tasks(self, **filters):
        # 查询实现
        pass
```

### 连接池监控
```python
def monitor_connection_pool():
    engine = get_engine()
    pool = engine.pool
    
    return {
        "pool_size": pool.size(),
        "checked_in": pool.checkedin(),
        "checked_out": pool.checkedout(),
        "overflow": pool.overflow(),
        "invalid": pool.invalidated()
    }
```

## 最佳实践

### 1. Repository设计原则
```python
# ✅ 好的设计
class TaskRepository:
    def get_pending_tasks(self) -> List[Task]:
        """获取待处理任务 - 明确的业务意图"""
        return self.session.query(Task).filter_by(state="pending").all()

# ❌ 避免的设计  
class TaskRepository:
    def query(self, **kwargs) -> List[Task]:
        """通用查询 - 缺乏明确意图"""
        # 过于通用，不表达业务含义
```

### 2. 错误处理
```python
class TaskRepository:
    def update_state(self, task_id: int, state: str) -> bool:
        try:
            task = self.get_by_id(task_id)
            if not task:
                return False
                
            task.state = state
            task.updated_at = datetime.utcnow()
            
            self.session.add(task)
            self.session.commit()
            return True
            
        except SQLAlchemyError as e:
            self.session.rollback()
            logger.error(f"Failed to update task {task_id}: {e}")
            return False
```

### 3. 查询优化
```python
# ✅ 使用连接查询避免N+1问题
def get_tasks_with_accounts(self):
    return (
        self.session.query(Task)
        .join(Account)
        .options(selectinload(Task.account))
        .all()
    )

# ❌ 会产生N+1查询问题
def get_tasks_with_accounts_bad(self):
    tasks = self.session.query(Task).all()
    for task in tasks:
        account = task.account  # 每次都会执行查询
```

## 故障排除

### 常见问题

1. **连接超时**
   - 检查数据库连接配置
   - 调整连接池参数
   - 验证网络连接

2. **事务死锁**
   - 检查事务范围
   - 调整隔离级别
   - 优化查询顺序

3. **内存泄漏**
   - 确保Session正确关闭
   - 避免长时间持有Session
   - 使用上下文管理器

### 调试技巧

```python
# 启用SQL日志
import logging
logging.getLogger('sqlalchemy.engine').setLevel(logging.INFO)

# Repository调试
def debug_query(query):
    """打印SQL查询"""
    print(f"SQL: {query}")
    print(f"Parameters: {query.compile().params}")
```

---

## 依赖管理

### 外部依赖
- `sqlmodel`: ORM和类型安全
- `sqlalchemy`: 底层数据库操作
- `psycopg2-binary`: PostgreSQL驱动
- `python-dateutil`: 日期处理

### 内部依赖  
- `backend.app.db.models`: 数据模型定义
- `backend.app.core.logging`: 日志记录

---

Repositories模块通过Repository模式提供了清晰、高效的数据访问层，确保了业务逻辑与数据持久化的分离，为Auto EM v2提供了稳定可靠的数据管理能力。