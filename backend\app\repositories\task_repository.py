from __future__ import annotations

import builtins
from datetime import datetime

from sqlmodel import Session, select

from backend.app.db.models import Task


class TaskRepository:
    def __init__(self, session: Session) -> None:
        self.session = session

    def create(self, kind: str, payload_json: str | None = None) -> Task:
        task = Task(kind=kind, state="pending", step=None, payload_json=payload_json)
        self.session.add(task)
        self.session.commit()
        self.session.refresh(task)
        return task

    def update_state(self, task_id: int, state: str, step: str | None = None, result_json: str | None = None) -> Task:
        task = self.session.get(Task, task_id)
        if not task:
            raise ValueError(f"Task {task_id} not found")
        task.state = state
        if step is not None:
            task.step = step
        if result_json is not None:
            task.result_json = result_json
        task.updated_at = datetime.utcnow()
        self.session.add(task)
        self.session.commit()
        self.session.refresh(task)
        return task

    def get(self, task_id: int) -> Task | None:
        return self.session.get(Task, task_id)

    def list(self, kind: str | None = None, state: str | None = None, limit: int = 100) -> builtins.list[Task]:
        query = select(Task)
        if kind:
            query = query.where(Task.kind == kind)
        if state:
            query = query.where(Task.state == state)
        query = query.order_by(Task.id.desc()).limit(limit)
        return list(self.session.exec(query))
