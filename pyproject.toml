[tool.poetry]
name = "auto-em-v2"
version = "2.0.0"
description = "自动邮箱注册工具 v2 - 多Provider支持与任务化引擎"
authors = ["Auto EM Team <<EMAIL>>"]
readme = "README.md"
packages = [{include = "backend"}]

[tool.poetry.dependencies]
python = "^3.11"
fastapi = "^0.104.0"
uvicorn = {extras = ["standard"], version = "^0.24.0"}
sqlmodel = "^0.0.14"
psycopg2-binary = "^2.9.7"
alembic = "^1.12.0"
requests = "^2.31.0"
httpx = "^0.24.0"
pydantic = "^2.4.0"
python-dotenv = "^1.0.0"
structlog = "^23.1.0"
python-multipart = "^0.0.6"

[tool.poetry.group.dev.dependencies]
pytest = "^7.4.0"
pytest-asyncio = "^0.21.0"
pytest-mock = "^3.11.0"
pytest-cov = "^4.1.0"
pytest-xdist = "^3.3.0"
ruff = "^0.1.0"
black = "^23.0.0"
mypy = "^1.5.0"
responses = "^0.23.0"
factory-boy = "^3.3.0"
testcontainers = {extras = ["postgres"], version = "^3.7.0"}

[tool.poetry.group.build.dependencies]
pyinstaller = "^6.0.0"
py2exe = {version = "^0.13.0", markers = "sys_platform == 'win32'"}
cx-freeze = "^6.15.0"

[tool.poetry.scripts]
auto-em-v2 = "backend.main:app"
auto-em-server = "backend.cli:start_server"
auto-em-worker = "backend.cli:start_worker"

[build-system]
requires = ["poetry-core"]
build-backend = "poetry.core.masonry.api"

[tool.pytest.ini_options]
minversion = "6.0"
addopts = "-ra -q --strict-markers"
testpaths = ["tests"]
python_files = "test_*.py"
python_classes = "Test*"
python_functions = "test_*"
markers = [
    "unit: Unit tests (isolated, fast)",
    "integration: Integration tests (with external deps)", 
    "contract: Contract tests for providers",
    "slow: Slow running tests",
    "network: Tests that require network access"
]

[tool.ruff]
line-length = 120
target-version = "py311"
select = [
    "E",  # pycodestyle errors
    "W",  # pycodestyle warnings
    "F",  # pyflakes
    "I",  # isort
    "B",  # flake8-bugbear
    "C4", # flake8-comprehensions
    "UP", # pyupgrade
]
ignore = [
    "E501", # line too long, handled by black
    "B008", # do not perform function calls in argument defaults
    "C901", # too complex
]

[tool.ruff.per-file-ignores]
"__init__.py" = ["F401"]
"tests/**/*" = ["B011", "S101"]

[tool.black]
line-length = 120
target-version = ['py311']
include = '\.pyi?$'
extend-exclude = '''
/(
  # directories
  \.eggs
  | \.git
  | \.hg
  | \.mypy_cache
  | \.tox
  | \.venv
  | build
  | dist
)/
'''

[tool.mypy]
python_version = "3.11"
warn_return_any = true
warn_unused_configs = true
disallow_untyped_defs = true
ignore_missing_imports = true
no_strict_optional = true

# PyInstaller配置
[tool.pyinstaller]
entry-point = "backend.main:app"
name = "auto-em-v2"
console = true
windowed = false
onefile = true
clean = true
noconfirm = true