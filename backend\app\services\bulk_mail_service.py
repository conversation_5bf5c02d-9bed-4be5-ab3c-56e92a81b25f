from __future__ import annotations

import json
import logging
import random
import secrets
import re
import time
from dataclasses import dataclass
from datetime import datetime
from typing import Any

from sqlmodel import Session

from backend.app.core.logging import log_event
from backend.app.db.models import Task
from backend.app.repositories.account_repository import AccountRepository
from backend.app.repositories.task_repository import TaskRepository


@dataclass
class BulkMailTemplate:
    """邮件模板"""

    subject: str
    content: str
    variables: dict[str, Any]
    randomization_rules: dict[str, Any] | None = None


@dataclass
class RateLimitConfig:
    """限速配置"""

    emails_per_hour: int = 100
    emails_per_day: int = 1000
    delay_between_emails: float = 30.0  # 秒
    randomize_delay: bool = True
    delay_variance: float = 0.3  # 延迟的随机变化范围


@dataclass
class BulkMailStats:
    """群发统计"""

    total_emails: int = 0
    sent_emails: int = 0
    failed_emails: int = 0
    pending_emails: int = 0
    start_time: datetime | None = None
    end_time: datetime | None = None
    accounts_used: int = 0


class BulkMailService:
    """
    群发邮件服务：支持模板变量、随机化、限速策略与账号池路由
    """

    def __init__(self):
        self.logger = logging.getLogger(__name__)
        self.active_tasks: dict[int, BulkMailStats] = {}

    def run(self, session: Session, task: Task) -> None:
        """
        执行群发邮件任务
        """
        repo = TaskRepository(session)
        account_repo = AccountRepository(session)

        try:
            # 解析任务payload
            payload = json.loads(task.payload_json or "{}")

            # 提取必要参数
            template_data = payload.get("template")
            recipient_list = payload.get("recipients", [])
            rate_limit_config = payload.get("rate_limit", {})
            account_pool = payload.get("account_pool", [])

            if not template_data:
                self._fail_task(repo, task, "MISSING_TEMPLATE", "Email template is required")
                return

            if not recipient_list:
                self._fail_task(repo, task, "MISSING_RECIPIENTS", "Recipient list is required")
                return

            if not account_pool:
                self._fail_task(repo, task, "MISSING_ACCOUNT_POOL", "Account pool is required")
                return

            # 创建模板和配置对象
            template = BulkMailTemplate(**template_data)
            rate_limit = RateLimitConfig(**rate_limit_config)

            # 初始化统计
            stats = BulkMailStats(
                total_emails=len(recipient_list), pending_emails=len(recipient_list), start_time=datetime.utcnow()
            )
            self.active_tasks[task.id] = stats

            log_event(
                self.logger,
                event="bulk_mail_start",
                task_id=task.id,
                extra={
                    "total_recipients": len(recipient_list),
                    "template_subject": template.subject,
                },
            )

            # 开始群发过程
            success = self._perform_bulk_send(session, task, template, recipient_list, rate_limit, account_pool, stats)

            if success:
                stats.end_time = datetime.utcnow()

                repo.update_state(
                    task.id,
                    state="succeeded",
                    step="completed",
                    result_json=json.dumps(
                        {
                            "ok": True,
                            "stats": {
                                "total": stats.total_emails,
                                "sent": stats.sent_emails,
                                "failed": stats.failed_emails,
                                "accounts_used": stats.accounts_used,
                                "duration_minutes": (stats.end_time - stats.start_time).total_seconds() / 60,
                            },
                        }
                    ),
                )

                log_event(
                    self.logger,
                    event="bulk_mail_completed",
                    task_id=task.id,
                    extra={
                        "sent_count": stats.sent_emails,
                        "failed_count": stats.failed_emails,
                    },
                )

        except Exception as e:
            self.logger.exception(f"Bulk mail task {task.id} failed with exception")
            self._fail_task(repo, task, "BULK_MAIL_EXCEPTION", str(e))
        finally:
            # 清理活跃任务记录
            self.active_tasks.pop(task.id, None)

    def _perform_bulk_send(
        self,
        session: Session,
        task: Task,
        template: BulkMailTemplate,
        recipients: list[str],
        rate_limit: RateLimitConfig,
        account_pool: list[str],
        stats: BulkMailStats,
    ) -> bool:
        """执行批量发送"""
        repo = TaskRepository(session)
        account_repo = AccountRepository(session)

        repo.update_state(task.id, state="running", step="bulk_sending")

        try:
            # 获取可用账户
            available_accounts = []
            for account_email in account_pool:
                account = account_repo.get_by_email(account_email)
                if account and account.state == "active":
                    available_accounts.append(account)

            if not available_accounts:
                self._fail_task(repo, task, "NO_AVAILABLE_ACCOUNTS", "No active accounts available")
                return False

            stats.accounts_used = len(available_accounts)

            # 开始发送邮件
            account_index = 0
            last_send_time = 0

            for i, recipient in enumerate(recipients):
                try:
                    # 选择账户（轮询策略）
                    current_account = available_accounts[account_index % len(available_accounts)]
                    account_index += 1

                    # 限速检查
                    current_time = time.time()
                    if last_send_time > 0:
                        time_since_last = current_time - last_send_time
                        required_delay = rate_limit.delay_between_emails

                        # 随机化延迟
                        if rate_limit.randomize_delay:
                            variance = required_delay * rate_limit.delay_variance
                            required_delay += random.uniform(-variance, variance)

                        if time_since_last < required_delay:
                            sleep_time = required_delay - time_since_last
                            time.sleep(sleep_time)

                    # 生成个性化邮件内容
                    personalized_subject = self._personalize_content(template.subject, recipient, i)
                    personalized_content = self._personalize_content(template.content, recipient, i)

                    # 模拟发送邮件
                    send_success = self._simulate_send_email(
                        current_account, recipient, personalized_subject, personalized_content
                    )

                    if send_success:
                        stats.sent_emails += 1
                        log_event(
                            self.logger,
                            event="email_sent",
                            task_id=task.id,
                            extra={
                                "recipient": recipient,
                                "sender_account": current_account.email,
                                "sequence_number": i + 1,
                            },
                        )
                    else:
                        stats.failed_emails += 1
                        log_event(
                            self.logger,
                            event="email_failed",
                            task_id=task.id,
                            outcome="send_failed",
                            extra={
                                "recipient": recipient,
                                "sender_account": current_account.email,
                                "sequence_number": i + 1,
                            },
                        )

                    stats.pending_emails -= 1
                    last_send_time = time.time()

                    # 定期更新任务状态
                    if (i + 1) % 10 == 0:
                        progress = {
                            "sent": stats.sent_emails,
                            "failed": stats.failed_emails,
                            "remaining": stats.pending_emails,
                            "progress_percent": ((i + 1) / len(recipients)) * 100,
                        }
                        repo.update_state(
                            task.id,
                            state="running",
                            step=f"sending_{i+1}_{len(recipients)}",
                            result_json=json.dumps(progress),
                        )

                except Exception as e:
                    self.logger.error(f"Failed to send email to {recipient}: {e}")
                    stats.failed_emails += 1
                    stats.pending_emails -= 1
                    continue

            return True

        except Exception as e:
            self.logger.exception(f"Bulk send process failed for task {task.id}")
            self._fail_task(repo, task, "BULK_SEND_ERROR", str(e))
            return False

    def _personalize_content(self, content: str, recipient: str, sequence: int) -> str:
        """个性化邮件内容"""
        try:
            # 基本变量替换
            personalized = content.replace("{recipient}", recipient)
            personalized = personalized.replace("{sequence}", str(sequence + 1))
            personalized = personalized.replace("{date}", datetime.now().strftime("%Y-%m-%d"))
            personalized = personalized.replace("{time}", datetime.now().strftime("%H:%M"))

            # 轻随机化处理
            personalized = self._apply_light_randomization(personalized)

            return personalized

        except Exception as e:
            self.logger.error(f"Failed to personalize content: {e}")
            return content  # 返回原始内容作为后备

    def _apply_light_randomization(self, content: str) -> str:
        """应用轻随机化"""
        try:
            # 同义词替换
            synonyms = {
                "你好": ["您好", "你好", "Hello"],
                "谢谢": ["感谢", "多谢", "Thanks"],
                "请": ["请", "烦请", "恳请"],
                "希望": ["期望", "希冀", "盼望"],
            }

            for original, alternatives in synonyms.items():
                if original in content:
                    replacement = secrets.choice(alternatives)
                    content = content.replace(original, replacement, 1)  # 只替换第一个

            # 添加随机的礼貌用语变化
            if "祝您" in content:
                greetings = ["祝您", "祝你", "愿您", "希望您"]
                content = content.replace("祝您", secrets.choice(greetings), 1)

            return content

        except Exception as e:
            self.logger.error(f"Failed to apply randomization: {e}")
            return content

    def _simulate_send_email(self, account, recipient: str, subject: str, content: str) -> bool:
        """
        模拟发送邮件
        在实际实现中，这里会调用真实的邮件发送API
        """
        try:
            # 模拟网络延迟
            time.sleep(random.uniform(0.1, 0.5))

            # 模拟成功率（90%成功率）
            success = random.random() > 0.1

            if success:
                log_event(
                    self.logger,
                    event="email_simulation",
                    outcome="success",
                    extra={
                        "recipient": recipient,
                        "subject": subject[:50],
                        "sender": account.email,
                    },
                )
            else:
                log_event(
                    self.logger,
                    event="email_simulation",
                    outcome="failed",
                    extra={
                        "recipient": recipient,
                        "subject": subject[:50],
                        "sender": account.email,
                    },
                )

            return success

        except Exception as e:
            self.logger.error(f"Email simulation failed: {e}")
            return False

    def get_task_stats(self, task_id: int) -> BulkMailStats | None:
        """获取任务统计信息"""
        return self.active_tasks.get(task_id)

    def _validate_template(self, template: BulkMailTemplate) -> bool:
        """验证邮件模板"""
        try:
            # 检查必需字段
            if not template.subject or not template.content:
                return False

            # 检查模板变量语法
            template_vars = re.findall(r"\{(\w+)\}", template.subject + template.content)
            supported_vars = ["recipient", "sequence", "date", "time"]

            for var in template_vars:
                if var not in supported_vars and var not in template.variables:
                    self.logger.warning(f"Unsupported template variable: {var}")

            return True

        except Exception as e:
            self.logger.error(f"Template validation failed: {e}")
            return False

    def _check_rate_limits(self, account_email: str, rate_limit: RateLimitConfig) -> bool:
        """检查发送频率限制"""
        try:
            # 在实际实现中，这里会检查数据库中的发送历史
            # 现在只是返回True表示通过检查
            return True

        except Exception as e:
            self.logger.error(f"Rate limit check failed: {e}")
            return False

    def _fail_task(self, repo: TaskRepository, task: Task, error_code: str, error_message: str):
        """标记任务失败"""
        repo.update_state(
            task.id,
            state="failed",
            step=task.step,
            result_json=json.dumps({"error": error_code, "message": error_message}),
        )

        log_event(
            self.logger, event="bulk_mail_failed", task_id=task.id, error_code=error_code, extra={"error_message": error_message}
        )
