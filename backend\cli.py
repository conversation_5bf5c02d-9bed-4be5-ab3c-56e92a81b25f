#!/usr/bin/env python3
"""
CLI模块 - 命令行界面和自动初始化功能
"""
import argparse
import logging
import sys
from pathlib import Path

try:
    import uvicorn
    from sqlmodel import SQLModel, create_engine

    from backend.app.core.config import settings
    from backend.app.core.logging import configure_logging
    from backend.app.db.session import engine, init_db
except ImportError as e:
    print(f"[ERROR] Import error: {e}")
    print("Please install dependencies: pip install -r requirements.txt")
    sys.exit(1)


def ensure_database_exists():
    """确保数据库存在，如果不存在则创建"""
    try:
        print("[INFO] Checking database connection...")

        # 尝试连接数据库
        from sqlalchemy import text

        with engine.connect() as conn:
            # 测试连接是否成功
            result = conn.execute(text("SELECT 1"))
            result.fetchone()

        print("[SUCCESS] Database connection successful")
        return True

    except Exception as e:
        print(f"[WARNING] Database connection failed: {e}")

        # 如果是SQLite，尝试创建文件
        database_url = str(settings.database_url) if hasattr(settings, "database_url") else None
        if database_url and database_url.startswith("sqlite"):
            try:
                # 提取SQLite文件路径
                if ":///" in database_url:
                    db_path = database_url.split(":///", 1)[1]
                    if db_path != ":memory:":
                        # 确保目录存在
                        db_dir = Path(db_path).parent
                        db_dir.mkdir(parents=True, exist_ok=True)
                        print(f"[DIR] Created database directory: {db_dir}")

                # 重新初始化数据库
                init_db()
                print("[SUCCESS] SQLite database initialized")
                return True

            except Exception as sqlite_e:
                print(f"[ERROR] Failed to initialize SQLite database: {sqlite_e}")
                return False

        # 对于PostgreSQL或其他数据库，提供建议
        print("[INFO] For PostgreSQL, please ensure:")
        print("   1. PostgreSQL server is running")
        print("   2. Database exists and is accessible")
        print("   3. Connection parameters are correct")
        return False


def initialize_application():
    """应用初始化 - 首次启动时执行"""
    print("[INIT] Initializing Auto EM v2...")

    # 配置日志
    try:
        configure_logging()
        print("[SUCCESS] Logging configured")
    except Exception as e:
        print(f"[WARNING] Logging configuration failed: {e}")
        # 使用基础日志配置
        logging.basicConfig(level=logging.INFO)

    # 检查并初始化数据库
    if not ensure_database_exists():
        print("[ERROR] Database initialization failed")
        return False

    # 创建数据库表
    try:
        print("[RUNNING] Creating database tables...")
        SQLModel.metadata.create_all(engine)
        print("[SUCCESS] Database tables created")
    except Exception as e:
        print(f"[ERROR] Failed to create database tables: {e}")
        return False

    print("[COMPLETE] Application initialized successfully!")
    return True


def start_server(host: str = "127.0.0.1", port: int = 8000, reload: bool = False):
    """启动Web服务器"""
    print(f"[SERVER] Starting Auto EM v2 server on {host}:{port}...")

    # 初始化应用
    if not initialize_application():
        print("[ERROR] Application initialization failed")
        sys.exit(1)

    try:
        # 导入FastAPI应用
        from backend.main import app

        # 启动uvicorn服务器
        uvicorn.run(
            app,
            host=host,
            port=port,
            reload=reload,
            log_level="info",
            access_log=True,
        )
    except Exception as e:
        print(f"[ERROR] Failed to start server: {e}")
        sys.exit(1)


def start_worker():
    """启动后台任务工作进程"""
    print("[WORKER] Starting Auto EM v2 worker...")

    # 初始化应用
    if not initialize_application():
        print("[ERROR] Application initialization failed")
        sys.exit(1)

    try:
        # 这里可以添加后台任务处理逻辑
        # 例如：定期清理过期任务、处理队列中的任务等
        from backend.app.db.session import get_session
        from backend.app.services.task_engine import TaskEngine

        def session_factory():
            return next(get_session())

        engine = TaskEngine(session_factory=session_factory)

        print("[SUCCESS] Worker started - monitoring tasks...")

        # 简单的工作循环（在实际应用中可能需要更复杂的任务队列）
        import time

        while True:
            try:
                # 这里可以添加定期任务
                # 例如：清理完成的任务、重试失败的任务等
                time.sleep(30)  # 每30秒检查一次
            except KeyboardInterrupt:
                print("\n[STOP] Worker stopped by user")
                break
            except Exception as e:
                print(f"[WARNING] Worker error: {e}")
                time.sleep(5)  # 出错后等待5秒再继续

    except Exception as e:
        print(f"[ERROR] Failed to start worker: {e}")
        sys.exit(1)


def check_health():
    """检查应用健康状态"""
    print("[CHECK] Checking Auto EM v2 health...")

    try:
        # 检查数据库连接
        if not ensure_database_exists():
            print("[ERROR] Database health check failed")
            return False

        # 检查依赖模块
        required_modules = ["fastapi", "uvicorn", "sqlmodel", "requests", "pydantic"]

        for module in required_modules:
            try:
                __import__(module)
                print(f"[SUCCESS] {module} - OK")
            except ImportError:
                print(f"[ERROR] {module} - Missing")
                return False

        print("[COMPLETE] All health checks passed!")
        return True

    except Exception as e:
        print(f"[ERROR] Health check failed: {e}")
        return False


def create_config_file():
    """创建示例配置文件"""
    config_content = """# Auto EM v2 配置文件
# 复制此文件为 .env 并修改相应配置

# 应用配置
APP_NAME=Auto EM v2
ENVIRONMENT=production
DEBUG=false

# 数据库配置
DATABASE_URL=sqlite:///./auto_em_v2.db
# PostgreSQL示例:
# DATABASE_URL=postgresql://username:password@localhost/auto_em_v2

# 日志配置
LOG_LEVEL=INFO
LOG_FORMAT=json

# Provider配置示例
# 验证码服务
CAPTCHA_PROVIDER=manual
CAPTCHA_API_KEY=your_api_key_here

# 短信服务
SMS_PROVIDER=5sim
SMS_API_KEY=your_api_key_here

# 代理服务
PROXY_PROVIDER=custom
PROXY_LIST=http://proxy1:8080,http://proxy2:8080

# 服务器配置
SERVER_HOST=127.0.0.1
SERVER_PORT=8000
"""

    try:
        with open(".env.example", "w", encoding="utf-8") as f:
            f.write(config_content)
        print("[SUCCESS] Created .env.example configuration file")
        print("[INFO] Copy .env.example to .env and modify settings as needed")
        return True
    except Exception as e:
        print(f"[ERROR] Failed to create config file: {e}")
        return False


def main():
    """CLI主入口"""
    parser = argparse.ArgumentParser(
        description="Auto EM v2 - 自动邮箱注册工具",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
  %(prog)s server                    # 启动Web服务器
  %(prog)s server --port 9000        # 在端口9000启动服务器
  %(prog)s worker                    # 启动后台工作进程
  %(prog)s init                      # 初始化应用和数据库
  %(prog)s health                    # 健康检查
  %(prog)s config                    # 创建示例配置文件
        """,
    )

    subparsers = parser.add_subparsers(dest="command", help="Available commands")

    # server命令
    server_parser = subparsers.add_parser("server", help="Start web server")
    server_parser.add_argument("--host", default="127.0.0.1", help="Server host (default: 127.0.0.1)")
    server_parser.add_argument("--port", type=int, default=8000, help="Server port (default: 8000)")
    server_parser.add_argument("--reload", action="store_true", help="Enable auto-reload for development")

    # worker命令
    subparsers.add_parser("worker", help="Start background worker")

    # init命令
    subparsers.add_parser("init", help="Initialize application and database")

    # health命令
    subparsers.add_parser("health", help="Check application health")

    # config命令
    subparsers.add_parser("config", help="Create example configuration file")

    args = parser.parse_args()

    if not args.command:
        # 如果没有指定命令，默认启动服务器
        print("No command specified, starting server...")
        start_server()
        return

    # 执行相应命令
    try:
        if args.command == "server":
            start_server(host=args.host, port=args.port, reload=args.reload)
        elif args.command == "worker":
            start_worker()
        elif args.command == "init":
            success = initialize_application()
            sys.exit(0 if success else 1)
        elif args.command == "health":
            success = check_health()
            sys.exit(0 if success else 1)
        elif args.command == "config":
            success = create_config_file()
            sys.exit(0 if success else 1)
        else:
            print(f"Unknown command: {args.command}")
            parser.print_help()
            sys.exit(1)

    except KeyboardInterrupt:
        print("\n[STOP] Interrupted by user")
        sys.exit(0)
    except Exception as e:
        print(f"[ERROR] Error: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()
