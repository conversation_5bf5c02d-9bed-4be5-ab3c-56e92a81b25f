from __future__ import annotations

from datetime import datetime

from sqlmodel import Field, SQLModel


class TimestampMixin(SQLModel):
    created_at: datetime = Field(default_factory=datetime.utcnow, nullable=False)
    updated_at: datetime = Field(default_factory=datetime.utcnow, nullable=False)


class Task(SQLModel, table=True):
    id: int | None = Field(default=None, primary_key=True)
    kind: str = Field(index=True, description="reg|nurture|mail")
    state: str = Field(index=True, description="pending|running|paused|succeeded|failed|canceled")
    step: str | None = Field(default=None)
    payload_json: str | None = None
    result_json: str | None = None
    created_at: datetime = Field(default_factory=datetime.utcnow, nullable=False)
    updated_at: datetime = Field(default_factory=datetime.utcnow, nullable=False)


class Account(SQLModel, table=True):
    id: int | None = Field(default=None, primary_key=True)
    email: str = Field(index=True, unique=True)
    password_encrypted: str
    oauth_token_encrypted: str | None = None
    imap_enabled: bool = Field(default=False)
    status: str = Field(default="pending", index=True)
    proxy_id: int | None = Field(default=None, foreign_key="providerconfig.id")
    created_at: datetime = Field(default_factory=datetime.utcnow, nullable=False)
    updated_at: datetime = Field(default_factory=datetime.utcnow, nullable=False)


class ProviderConfig(SQLModel, table=True):
    id: int | None = Field(default=None, primary_key=True)
    provider_type: str = Field(index=True, description="captcha|sms|proxy")
    name: str = Field(index=True)
    config_encrypted: str
    is_active: bool = Field(default=True, index=True)
    created_at: datetime = Field(default_factory=datetime.utcnow, nullable=False)
    updated_at: datetime = Field(default_factory=datetime.utcnow, nullable=False)
