import flet as ft
import httpx

API_BASE = "http://127.0.0.1:8000"


def main(page: ft.Page):
    page.title = "Auto EM v2"

    log = ft.Text(value="", selectable=True)
    last_task_id = ft.Text(value="", selectable=True)

    # Provider test inputs
    captcha_key = ft.TextField(label="2captcha API Key", password=True, can_reveal_password=True, width=350)
    sms_key = ft.TextField(label="SMS-Activate API Key", password=True, can_reveal_password=True, width=350)
    proxies = ft.TextField(label="代理列表(逗号分隔)", width=500)

    async def call_health(e=None):
        try:
            async with httpx.AsyncClient(timeout=5) as client:
                r = await client.get(f"{API_BASE}/health")
                log.value = f"/health → {r.status_code} {r.text}"
        except Exception as ex:
            log.value = f"/health error: {ex}"
        await page.update_async()

    async def start_reg(e=None):
        try:
            async with httpx.AsyncClient(timeout=5) as client:
                r = await client.post(f"{API_BASE}/tasks/start/reg", json={})
                log.value = f"start reg → {r.status_code} {r.text}"
                try:
                    data = r.json()
                    if "id" in data:
                        last_task_id.value = str(data["id"]) or ""
                except Exception:
                    pass
        except Exception as ex:
            log.value = f"start reg error: {ex}"
        await page.update_async()

    async def refresh_task(e=None):
        try:
            tid = (last_task_id.value or "").strip()
            if not tid:
                log.value = "未记录任务ID，请先启动一次任务"
            else:
                async with httpx.AsyncClient(timeout=5) as client:
                    r = await client.get(f"{API_BASE}/tasks/{tid}")
                    log.value = f"task {tid} → {r.status_code} {r.text}"
        except Exception as ex:
            log.value = f"refresh task error: {ex}"
        await page.update_async()

    async def test_captcha(e=None):
        try:
            key = (captcha_key.value or "").strip()
            if not key:
                log.value = "请输入 2captcha API Key 后再测试"
            else:
                async with httpx.AsyncClient(timeout=10) as client:
                    r = await client.post(
                        f"{API_BASE}/providers/test",
                        json={"type": "captcha", "name": "2captcha", "config": {"api_key": key}},
                    )
                    log.value = f"captcha test → {r.status_code} {r.text}"
        except Exception as ex:
            log.value = f"captcha test error: {ex}"
        await page.update_async()

    async def test_sms(e=None):
        try:
            key = (sms_key.value or "").strip()
            if not key:
                log.value = "请输入 SMS-Activate API Key 后再测试"
            else:
                async with httpx.AsyncClient(timeout=10) as client:
                    r = await client.post(
                        f"{API_BASE}/providers/test",
                        json={"type": "sms", "name": "sms-activate", "config": {"api_key": key}},
                    )
                    log.value = f"sms test → {r.status_code} {r.text}"
        except Exception as ex:
            log.value = f"sms test error: {ex}"
        await page.update_async()

    async def test_proxy(e=None):
        try:
            csv = (proxies.value or "").strip()
            if not csv:
                log.value = "请输入代理列表后再测试"
            else:
                async with httpx.AsyncClient(timeout=10) as client:
                    r = await client.post(
                        f"{API_BASE}/providers/test",
                        json={"type": "proxy", "name": "custom", "config": {"proxies": csv}},
                    )
                    log.value = f"proxy test → {r.status_code} {r.text}"
        except Exception as ex:
            log.value = f"proxy test error: {ex}"
        await page.update_async()

    btn_health = ft.ElevatedButton("健康检查", on_click=call_health)
    btn_start = ft.ElevatedButton("启动注册任务", on_click=start_reg)
    btn_refresh = ft.ElevatedButton("刷新任务状态", on_click=refresh_task)
    btn_test_captcha = ft.ElevatedButton("测试 2captcha", on_click=test_captcha)
    btn_test_sms = ft.ElevatedButton("测试 SMS-Activate", on_click=test_sms)
    btn_test_proxy = ft.ElevatedButton("测试代理池", on_click=test_proxy)

    page.add(
        ft.Column(
            [
                ft.Row([btn_health, btn_start, btn_refresh]),
                ft.Row([ft.Text("Last Task ID:"), last_task_id]),
                ft.Divider(),
                ft.Text("Provider 测试"),
                ft.Row([captcha_key, btn_test_captcha]),
                ft.Row([sms_key, btn_test_sms]),
                ft.Row([proxies, btn_test_proxy]),
                ft.Divider(),
                log,
            ]
        )
    )


if __name__ == "__main__":
    ft.app(target=main)
