"""
Pytest配置文件 - 全局测试配置和fixture
"""

import os
import sys
from unittest.mock import Mock

import pytest

# 添加backend目录到Python路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), "..", "backend"))


@pytest.fixture(autouse=True)
def mock_logging():
    """自动mock logging避免测试时的日志输出干扰"""
    import logging

    logging.disable(logging.CRITICAL)
    yield
    logging.disable(logging.NOTSET)


@pytest.fixture
def mock_database_session():
    """通用的mock数据库session"""
    session = Mock()
    session.add = Mock()
    session.commit = Mock()
    session.refresh = Mock()
    session.query = Mock()
    return session


@pytest.fixture
def sample_task_data():
    """示例任务数据"""
    return {
        "id": 1,
        "kind": "reg",
        "state": "pending",
        "step": "init",
        "payload_json": '{"email": "<EMAIL>"}',
        "result_json": None,
    }


@pytest.fixture
def mock_provider_responses():
    """Mock provider响应数据"""
    return {
        "captcha": {"test_connectivity": {"ok": True, "balance": 100}, "solve": "ABCD123"},
        "sms": {
            "test_connectivity": {"ok": True, "balance": 50.5},
            "get_number": {"activation_id": "12345", "phone_number": "+**********"},
            "poll_code": "123456",
        },
        "proxy": {"test_connectivity": {"ok": True, "count": 10}, "next": "http://proxy.example.com:8080"},
    }


# 标记配置
def pytest_configure(config):
    """配置pytest标记"""
    config.addinivalue_line("markers", "unit: Unit tests (fast, isolated)")
    config.addinivalue_line("markers", "integration: Integration tests (with external deps)")
    config.addinivalue_line("markers", "contract: Contract tests for providers")
    config.addinivalue_line("markers", "slow: Slow running tests")
    config.addinivalue_line("markers", "network: Tests requiring network access")


def pytest_collection_modifyitems(config, items):
    """修改测试收集，为没有标记的测试添加默认标记"""
    for item in items:
        # 为没有标记的测试添加unit标记
        if not any(item.iter_markers()):
            item.add_marker(pytest.mark.unit)

        # 为网络测试添加skipif条件
        if item.get_closest_marker("network"):
            skip_network = pytest.mark.skipif(
                os.getenv("SKIP_NETWORK_TESTS", "false").lower() == "true", reason="Network tests skipped"
            )
            item.add_marker(skip_network)


# 环境变量设置用于测试
@pytest.fixture(autouse=True)
def setup_test_environment(monkeypatch):
    """设置测试环境变量"""
    monkeypatch.setenv("ENVIRONMENT", "test")
    monkeypatch.setenv("DATABASE_URL", "sqlite:///:memory:")
    monkeypatch.setenv("LOG_LEVEL", "ERROR")


# 清理fixture
@pytest.fixture(autouse=True)
def cleanup_after_test():
    """测试后清理"""
    yield
    # 这里可以添加测试后清理逻辑
    pass
