from __future__ import annotations

import json
import logging
import random
import time
from datetime import datetime

from sqlmodel import Session

from backend.app.core.logging import log_event
from backend.app.db.models import Task
from backend.app.providers.factory import create_proxy_provider
from backend.app.repositories.account_repository import AccountRepository
from backend.app.repositories.task_repository import TaskRepository


class NurtureService:
    """
    养护服务：负责已注册账户的日常维护
    包括周期性登录、邮件标记已读、资料更新等功能
    """

    def __init__(self):
        self.logger = logging.getLogger(__name__)

    def run(self, session: Session, task: Task) -> None:
        """
        执行养护任务
        根据任务payload中的nurture_type确定具体的养护操作
        """
        repo = TaskRepository(session)
        account_repo = AccountRepository(session)

        try:
            # 解析任务payload
            payload = json.loads(task.payload_json or "{}")
            nurture_type = payload.get("nurture_type", "login_check")
            account_id = payload.get("account_id")

            if not account_id:
                self._fail_task(repo, task, "MISSING_ACCOUNT_ID", "Account ID is required")
                return

            # 获取账户信息
            account = account_repo.get_by_id(account_id)
            if not account:
                self._fail_task(repo, task, "ACCOUNT_NOT_FOUND", f"Account {account_id} not found")
                return

            log_event(
                self.logger,
                event="nurture_start",
                task_id=task.id,
                account_id=account_id,
                extra={"nurture_type": nurture_type}
            )

            # 根据养护类型执行相应操作
            if nurture_type == "login_check":
                success = self._perform_login_check(session, task, account, payload)
            elif nurture_type == "mark_emails_read":
                success = self._perform_mark_emails_read(session, task, account, payload)
            elif nurture_type == "update_profile":
                success = self._perform_update_profile(session, task, account, payload)
            elif nurture_type == "maintenance":
                success = self._perform_general_maintenance(session, task, account, payload)
            else:
                self._fail_task(repo, task, "UNKNOWN_NURTURE_TYPE", f"Unknown nurture type: {nurture_type}")
                return

            if success:
                # 更新账户的最后养护时间
                account_repo.update_last_nurtured(account_id, datetime.utcnow())

                repo.update_state(
                    task.id,
                    state="succeeded",
                    step="completed",
                    result_json=json.dumps({"ok": True, "nurture_type": nurture_type}),
                )

                log_event(
                    self.logger,
                    event="nurture_success",
                    task_id=task.id,
                    account_id=account_id,
                    extra={"nurture_type": nurture_type},
                )

        except Exception as e:
            self.logger.exception(f"Nurture task {task.id} failed with exception")
            self._fail_task(repo, task, "NURTURE_EXCEPTION", str(e))

    def _perform_login_check(self, session: Session, task: Task, account, payload: dict) -> bool:
        """执行登录检查"""
        repo = TaskRepository(session)

        repo.update_state(task.id, state="running", step="login_check")

        try:
            # 选择代理
            proxy = self._select_proxy()

            # 模拟登录检查过程
            login_success = self._simulate_login(account.email, proxy)

            if login_success:
                log_event(self.logger, event="login_check_success", task_id=task.id, extra={"account_email": account.email})
                return True
            else:
                self._fail_task(repo, task, "LOGIN_FAILED", "Login check failed")
                return False

        except Exception as e:
            self.logger.exception(f"Login check failed for account {account.email}")
            self._fail_task(repo, task, "LOGIN_CHECK_ERROR", str(e))
            return False

    def _perform_mark_emails_read(self, session: Session, task: Task, account, payload: dict) -> bool:
        """标记邮件为已读"""
        repo = TaskRepository(session)

        repo.update_state(task.id, state="running", step="mark_emails_read")

        try:
            # 获取邮件数量限制
            max_emails = payload.get("max_emails", 10)

            # 模拟标记邮件已读的过程
            marked_count = self._simulate_mark_emails_read(account.email, max_emails)

            log_event(
                self.logger,
                event="emails_marked_read",
                task_id=task.id,
                extra={"account_email": account.email, "marked_count": marked_count},
            )

            return True

        except Exception as e:
            self.logger.exception(f"Mark emails read failed for account {account.email}")
            self._fail_task(repo, task, "MARK_EMAILS_ERROR", str(e))
            return False

    def _perform_update_profile(self, session: Session, task: Task, account, payload: dict) -> bool:
        """更新账户资料"""
        repo = TaskRepository(session)

        repo.update_state(task.id, state="running", step="update_profile")

        try:
            # 获取更新类型
            update_type = payload.get("update_type", "avatar")  # avatar, nickname, signature

            # 模拟资料更新过程
            update_success = self._simulate_profile_update(account.email, update_type, payload)

            if update_success:
                log_event(
                    self.logger,
                    event="profile_updated",
                    task_id=task.id,
                    extra={"account_email": account.email, "update_type": update_type},
                )
                return True
            else:
                self._fail_task(repo, task, "PROFILE_UPDATE_FAILED", f"Failed to update {update_type}")
                return False

        except Exception as e:
            self.logger.exception(f"Profile update failed for account {account.email}")
            self._fail_task(repo, task, "PROFILE_UPDATE_ERROR", str(e))
            return False

    def _perform_general_maintenance(self, session: Session, task: Task, account, payload: dict) -> bool:
        """执行综合维护"""
        repo = TaskRepository(session)

        repo.update_state(task.id, state="running", step="general_maintenance")

        try:
            maintenance_tasks = payload.get("tasks", ["login_check", "mark_emails_read"])
            completed_tasks = []

            for maintenance_task in maintenance_tasks:
                if maintenance_task == "login_check":
                    if self._simulate_login(account.email, self._select_proxy()):
                        completed_tasks.append("login_check")
                elif maintenance_task == "mark_emails_read":
                    marked = self._simulate_mark_emails_read(account.email, 5)
                    if marked >= 0:
                        completed_tasks.append("mark_emails_read")

                # 短暂延迟避免过于频繁的操作
                time.sleep(0.5)

            log_event(
                self.logger,
                event="maintenance_completed",
                task_id=task.id,
                extra={"account_email": account.email, "completed_tasks": completed_tasks},
            )

            return len(completed_tasks) > 0

        except Exception as e:
            self.logger.exception(f"General maintenance failed for account {account.email}")
            self._fail_task(repo, task, "MAINTENANCE_ERROR", str(e))
            return False

    def _select_proxy(self) -> str | None:
        """选择代理（复用注册管道的代理机制）"""
        try:
            provider = create_proxy_provider("custom", {"proxies": ""})
            return provider.next()
        except Exception:
            return None

    def _simulate_login(self, email: str, proxy: str | None) -> bool:
        """
        模拟登录过程
        在实际实现中，这里会进行真实的登录验证
        """
        import requests

        try:
            proxies = {"http": proxy, "https": proxy} if proxy else None
            headers = {"User-Agent": "Mozilla/5.0"}

            # 模拟访问登录页面
            response = requests.get("https://login.live.com/", headers=headers, timeout=10, proxies=proxies)

            # 简单的成功判断（实际实现会更复杂）
            return response.status_code == 200

        except Exception:
            return False

    def _simulate_mark_emails_read(self, email: str, max_emails: int) -> int:
        """
        模拟标记邮件已读
        返回标记的邮件数量
        """
        # 在实际实现中，这里会连接到邮箱服务标记邮件
        # 现在只是返回一个模拟数量
        return random.randint(0, min(max_emails, 20))

    def _simulate_profile_update(self, email: str, update_type: str, payload: dict) -> bool:
        """
        模拟资料更新
        """
        # 在实际实现中，这里会进行真实的资料更新
        # 现在只是模拟成功

        if update_type == "avatar":
            avatar_path = payload.get("avatar_path")
            # 检查头像文件是否存在（占位逻辑）
            return avatar_path is not None
        elif update_type == "nickname":
            nickname = payload.get("nickname")
            return nickname is not None and len(nickname.strip()) > 0
        elif update_type == "signature":
            signature = payload.get("signature")
            return signature is not None

        return False

    def _fail_task(self, repo: TaskRepository, task: Task, error_code: str, error_message: str):
        """标记任务失败"""
        repo.update_state(
            task.id,
            state="failed",
            step=task.step,
            result_json=json.dumps({"error": error_code, "message": error_message}),
        )

        log_event(
            self.logger, event="nurture_failed", task_id=task.id, error_code=error_code, extra={"error_message": error_message}
        )
