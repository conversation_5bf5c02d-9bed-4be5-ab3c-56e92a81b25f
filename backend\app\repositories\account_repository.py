from __future__ import annotations

import builtins
from datetime import datetime

from sqlmodel import Session, select

from backend.app.db.models import Account


class AccountRepository:
    def __init__(self, session: Session) -> None:
        self.session = session

    def create(self, email: str, password_encrypted: str) -> Account:
        account = Account(email=email, password_encrypted=password_encrypted)
        self.session.add(account)
        self.session.commit()
        self.session.refresh(account)
        return account

    def update_status(
        self,
        account_id: int,
        *,
        imap_enabled: bool | None = None,
        oauth_token_encrypted: str | None = None,
        status: str | None = None,
    ) -> Account:
        account = self.session.get(Account, account_id)
        if not account:
            raise ValueError(f"Account {account_id} not found")
        if imap_enabled is not None:
            account.imap_enabled = imap_enabled
        if oauth_token_encrypted is not None:
            account.oauth_token_encrypted = oauth_token_encrypted
        if status is not None:
            account.status = status
        self.session.add(account)
        self.session.commit()
        self.session.refresh(account)
        return account

    def get_by_id(self, account_id: int) -> Account | None:
        return self.session.get(Account, account_id)

    def get_by_email(self, email: str) -> Account | None:
        statement = select(Account).where(Account.email == email)
        return self.session.exec(statement).first()

    def find_by_email(self, email: str) -> Account | None:
        statement = select(Account).where(Account.email == email)
        return self.session.exec(statement).first()

    def update_last_nurtured(self, account_id: int, last_nurtured: datetime) -> Account:
        account = self.session.get(Account, account_id)
        if not account:
            raise ValueError(f"Account {account_id} not found")
        account.updated_at = last_nurtured
        self.session.add(account)
        self.session.commit()
        self.session.refresh(account)
        return account

    def list(self, status: str | None = None, limit: int = 100) -> builtins.list[Account]:
        statement = select(Account)
        if status:
            statement = statement.where(Account.status == status)
        statement = statement.order_by(Account.id.desc()).limit(limit)
        return list(self.session.exec(statement))
