# Auto EM v2 配置文件
# 复制此文件为 .env 并修改相应配置

# 应用配置
APP_NAME=Auto EM v2
ENVIRONMENT=production
DEBUG=false

# 数据库配置
DATABASE_URL=sqlite:///./auto_em_v2.db
# PostgreSQL示例:
# DATABASE_URL=postgresql://username:password@localhost/auto_em_v2

# 日志配置
LOG_LEVEL=INFO
LOG_FORMAT=json

# Provider配置示例
# 验证码服务
CAPTCHA_PROVIDER=manual
CAPTCHA_API_KEY=your_api_key_here

# 短信服务
SMS_PROVIDER=5sim
SMS_API_KEY=your_api_key_here

# 代理服务
PROXY_PROVIDER=custom
PROXY_LIST=http://proxy1:8080,http://proxy2:8080

# 服务器配置
SERVER_HOST=127.0.0.1
SERVER_PORT=8000