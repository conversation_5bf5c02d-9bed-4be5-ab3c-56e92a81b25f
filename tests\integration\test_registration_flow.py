"""
集成测试 - 测试完整的注册任务流程
启动注册任务 → 查询状态 → 完成（使用替身/假实现）
"""

import json
import time
from unittest.mock import patch

import pytest
from sqlmodel import Session, create_engine
from testcontainers.postgres import PostgresContainer

from backend.app.services.registration_pipeline import RegistrationPipelineService
from backend.app.services.task_engine import TaskEngine


class TestRegistrationIntegrationFlow:
    """集成测试：完整注册流程"""

    @pytest.fixture(scope="class")
    def test_engine(self):
        """创建测试数据库引擎"""
        # 使用内存SQLite进行快速测试
        from sqlmodel import create_engine

        engine = create_engine("sqlite:///:memory:")
        return engine

    @pytest.fixture
    def test_session(self, test_engine):
        """创建测试session"""
        # 初始化数据库表
        from sqlmodel import SQLModel

        SQLModel.metadata.create_all(test_engine)

        with Session(test_engine) as session:
            yield session

    @pytest.fixture
    def task_engine(self, test_session):
        """创建TaskEngine实例用于测试"""

        def session_factory():
            return test_session

        engine = TaskEngine(session_factory=session_factory)
        # 注册测试用的registration runner
        engine.register_runner("reg", RegistrationPipelineService().run)
        return engine

    @pytest.fixture
    def mock_successful_pipeline(self):
        """Mock成功的pipeline执行"""
        with (
            patch.object(RegistrationPipelineService, "_select_proxy", return_value="http://test-proxy:8080"),
            patch.object(RegistrationPipelineService, "_fetch_signup_form", return_value=True),
            patch.object(RegistrationPipelineService, "_submit_registration", return_value=(True, False, False)),
            patch("backend.app.services.registration_pipeline.time.sleep"),
        ):  # 跳过sleep延迟
            yield

    @pytest.mark.integration
    def test_complete_registration_flow_success(self, task_engine, mock_successful_pipeline):
        """测试完整的成功注册流程"""
        # 1. 启动注册任务
        task = task_engine.start("reg", {"email": "<EMAIL>"})
        assert task is not None
        assert task.kind == "reg"
        assert task.state == "pending"

        initial_task_id = task.id

        # 2. 查询初始状态
        retrieved_task = task_engine.get(initial_task_id)
        assert retrieved_task is not None
        assert retrieved_task.id == initial_task_id
        assert retrieved_task.state == "pending"

        # 3. 等待任务执行完成（模拟异步处理）
        # 注意：在真实环境中，任务会在后台异步执行
        # 这里我们手动触发执行来模拟完成状态
        max_retries = 10
        for i in range(max_retries):
            retrieved_task = task_engine.get(initial_task_id)
            if retrieved_task.state in ["succeeded", "failed"]:
                break
            time.sleep(0.1)  # 短暂等待

        # 4. 验证最终状态
        final_task = task_engine.get(initial_task_id)
        assert final_task is not None
        assert final_task.state == "succeeded"
        assert final_task.step == "done"

        # 5. 验证结果
        if final_task.result_json:
            result = json.loads(final_task.result_json)
            assert result.get("ok") is True

    @pytest.mark.integration
    def test_registration_flow_with_fetch_failure(self, task_engine):
        """测试注册流程中获取表单失败的情况"""
        # Mock失败的表单获取
        with (
            patch.object(RegistrationPipelineService, "_select_proxy", return_value="http://test-proxy:8080"),
            patch.object(RegistrationPipelineService, "_fetch_signup_form", return_value=False),
            patch("backend.app.services.registration_pipeline.time.sleep"),
        ):

            # 启动任务
            task = task_engine.start("reg", {"email": "<EMAIL>"})
            task_id = task.id

            # 等待执行完成
            max_retries = 10
            for i in range(max_retries):
                retrieved_task = task_engine.get(task_id)
                if retrieved_task.state in ["succeeded", "failed"]:
                    break
                time.sleep(0.1)

            # 验证失败状态
            final_task = task_engine.get(task_id)
            assert final_task.state == "failed"
            assert final_task.step == "fetch_signup_form"

            if final_task.result_json:
                result = json.loads(final_task.result_json)
                assert result.get("error") == "FETCH_FORM_FAIL"

    @pytest.mark.integration
    def test_registration_flow_with_submit_failure(self, task_engine):
        """测试注册流程中提交失败的情况"""
        # Mock提交失败
        with (
            patch.object(RegistrationPipelineService, "_select_proxy", return_value="http://test-proxy:8080"),
            patch.object(RegistrationPipelineService, "_fetch_signup_form", return_value=True),
            patch.object(RegistrationPipelineService, "_submit_registration", return_value=(False, False, False)),
            patch("backend.app.services.registration_pipeline.time.sleep"),
        ):

            # 启动任务
            task = task_engine.start("reg", {"email": "<EMAIL>"})
            task_id = task.id

            # 等待执行完成
            max_retries = 10
            for i in range(max_retries):
                retrieved_task = task_engine.get(task_id)
                if retrieved_task.state in ["succeeded", "failed"]:
                    break
                time.sleep(0.1)

            # 验证失败状态
            final_task = task_engine.get(task_id)
            assert final_task.state == "failed"
            assert final_task.step == "submit_registration"

            if final_task.result_json:
                result = json.loads(final_task.result_json)
                assert result.get("error") == "SUBMIT_FAIL"

    @pytest.mark.integration
    def test_multiple_concurrent_tasks(self, task_engine, mock_successful_pipeline):
        """测试多个并发注册任务"""
        # 启动多个任务
        tasks = []
        for i in range(3):
            task = task_engine.start("reg", {"email": f"test{i}@example.com"})
            tasks.append(task)

        # 验证所有任务都被创建
        assert len(tasks) == 3
        for task in tasks:
            assert task.kind == "reg"
            assert task.state == "pending"

        # 等待所有任务执行完成
        completed_tasks = []
        max_retries = 20
        for i in range(max_retries):
            completed_tasks = []
            for task in tasks:
                retrieved_task = task_engine.get(task.id)
                if retrieved_task.state in ["succeeded", "failed"]:
                    completed_tasks.append(retrieved_task)

            if len(completed_tasks) == len(tasks):
                break
            time.sleep(0.1)

        # 验证所有任务都完成
        assert len(completed_tasks) == 3
        for task in completed_tasks:
            assert task.state == "succeeded"

    @pytest.mark.integration
    def test_task_listing_and_filtering(self, task_engine, mock_successful_pipeline):
        """测试任务列表和过滤功能"""
        # 创建不同类型的任务
        reg_task = task_engine.start("reg", {"email": "<EMAIL>"})

        # 等待任务完成
        time.sleep(0.2)

        # 测试列出所有任务
        all_tasks = task_engine.list()
        assert len(all_tasks) >= 1

        # 测试按类型过滤
        reg_tasks = task_engine.list(kind="reg")
        assert len(reg_tasks) >= 1
        assert all(task.kind == "reg" for task in reg_tasks)

        # 测试按状态过滤
        completed_task = task_engine.get(reg_task.id)
        if completed_task.state == "succeeded":
            succeeded_tasks = task_engine.list(state="succeeded")
            assert len(succeeded_tasks) >= 1
            assert all(task.state == "succeeded" for task in succeeded_tasks)

    @pytest.mark.integration
    def test_nonexistent_task_retrieval(self, task_engine):
        """测试获取不存在的任务"""
        # 尝试获取不存在的任务
        nonexistent_task = task_engine.get(99999)
        assert nonexistent_task is None

    @pytest.mark.integration
    def test_invalid_runner_type(self, task_engine):
        """测试启动无效的runner类型"""
        # 尝试启动不存在的runner
        with pytest.raises(KeyError):
            task_engine.start("invalid_runner", {})


class TestWithRealDatabase:
    """使用真实数据库的集成测试（可选）"""

    @pytest.mark.slow
    @pytest.mark.skipif(
        not pytest.importorskip("testcontainers", reason="testcontainers not available"),
        reason="Testcontainers required for real database tests",
    )
    def test_with_postgres_container(self):
        """使用PostgreSQL容器的完整集成测试"""
        try:
            with PostgresContainer("postgres:13") as postgres:
                # 创建引擎连接到容器数据库
                database_url = postgres.get_connection_url()
                engine = create_engine(database_url)

                # 初始化数据库
                from sqlmodel import SQLModel

                SQLModel.metadata.create_all(engine)

                def session_factory():
                    return Session(engine)

                # 创建task engine
                task_engine = TaskEngine(session_factory=session_factory)
                task_engine.register_runner("reg", RegistrationPipelineService().run)

                # Mock成功的pipeline
                with (
                    patch.object(RegistrationPipelineService, "_select_proxy", return_value="http://test-proxy:8080"),
                    patch.object(RegistrationPipelineService, "_fetch_signup_form", return_value=True),
                    patch.object(
                        RegistrationPipelineService, "_submit_registration", return_value=(True, False, False)
                    ),
                    patch("backend.app.services.registration_pipeline.time.sleep"),
                ):

                    # 执行基本的任务流程测试
                    task = task_engine.start("reg", {"email": "<EMAIL>"})
                    assert task is not None
                    assert task.kind == "reg"

                    # 等待完成
                    max_retries = 10
                    for i in range(max_retries):
                        retrieved_task = task_engine.get(task.id)
                        if retrieved_task.state in ["succeeded", "failed"]:
                            break
                        time.sleep(0.2)

                    final_task = task_engine.get(task.id)
                    assert final_task.state == "succeeded"

        except ImportError:
            pytest.skip("testcontainers not available")
