"""
RegistrationPipeline 单元测试 - 使用mock repository和provider
"""

import json
from unittest.mock import Mock, patch

import pytest
from sqlmodel import Session

from backend.app.db.models import Task
from backend.app.services.registration_pipeline import RegistrationPipelineService


class TestRegistrationPipelineService:
    """RegistrationPipelineService 单元测试"""

    @pytest.fixture
    def mock_session(self):
        """创建mock session"""
        return Mock(spec=Session)

    @pytest.fixture
    def mock_task(self):
        """创建mock task"""
        task = Mock(spec=Task)
        task.id = 1
        task.state = "pending"
        task.step = "init"
        return task

    @pytest.fixture
    def mock_task_repository(self):
        """创建mock task repository"""
        return Mock()

    @pytest.fixture
    def pipeline_service(self):
        """创建RegistrationPipelineService实例"""
        return RegistrationPipelineService()

    @patch("backend.app.services.registration_pipeline.TaskRepository")
    @patch("backend.app.services.registration_pipeline.log_event")
    @patch("backend.app.services.registration_pipeline.logging.getLogger")
    def test_run_success_flow(
        self, mock_get_logger, mock_log_event, mock_task_repo_class, pipeline_service, mock_session, mock_task
    ):
        """测试成功的注册流程"""
        # Setup mocks
        mock_repo = Mock()
        mock_task_repo_class.return_value = mock_repo
        mock_logger = Mock()
        mock_get_logger.return_value = mock_logger

        # Mock the helper methods to return successful results
        with (
            patch.object(pipeline_service, "_select_proxy", return_value="http://proxy:8080"),
            patch.object(pipeline_service, "_fetch_signup_form", return_value=True),
            patch.object(pipeline_service, "_submit_registration", return_value=(True, False, False)),
        ):

            # Execute
            pipeline_service.run(mock_session, mock_task)

            # Verify repository was initialized with session
            mock_task_repo_class.assert_called_once_with(mock_session)

            # Verify state updates - should be called multiple times for different steps
            assert mock_repo.update_state.call_count >= 6  # At least 6 steps

            # Verify final success state
            final_call = mock_repo.update_state.call_args_list[-1]
            assert final_call[1]["state"] == "succeeded"
            assert final_call[1]["step"] == "done"

            # Verify logging was called
            assert mock_log_event.call_count > 0

    @patch("backend.app.services.registration_pipeline.TaskRepository")
    @patch("backend.app.services.registration_pipeline.log_event")
    @patch("backend.app.services.registration_pipeline.logging.getLogger")
    def test_run_fetch_form_failure(
        self, mock_get_logger, mock_log_event, mock_task_repo_class, pipeline_service, mock_session, mock_task
    ):
        """测试获取注册表单失败的情况"""
        # Setup mocks
        mock_repo = Mock()
        mock_task_repo_class.return_value = mock_repo
        mock_logger = Mock()
        mock_get_logger.return_value = mock_logger

        # Mock helper methods - fetch_signup_form fails
        with (
            patch.object(pipeline_service, "_select_proxy", return_value="http://proxy:8080"),
            patch.object(pipeline_service, "_fetch_signup_form", return_value=False),
        ):

            # Execute
            pipeline_service.run(mock_session, mock_task)

            # Verify failure state was set
            mock_repo.update_state.assert_called_with(
                mock_task.id,
                state="failed",
                step="fetch_signup_form",
                result_json=json.dumps({"error": "FETCH_FORM_FAIL"}),
            )

            # Verify error was logged
            mock_log_event.assert_called_with(
                mock_logger,
                event="pipeline_fail",
                task_id=mock_task.id,
                step="fetch_signup_form",
                outcome="failed",
                error_code="FETCH_FORM_FAIL",
            )

    @patch("backend.app.services.registration_pipeline.TaskRepository")
    @patch("backend.app.services.registration_pipeline.log_event")
    @patch("backend.app.services.registration_pipeline.logging.getLogger")
    def test_run_submit_registration_failure(
        self, mock_get_logger, mock_log_event, mock_task_repo_class, pipeline_service, mock_session, mock_task
    ):
        """测试提交注册失败的情况"""
        # Setup mocks
        mock_repo = Mock()
        mock_task_repo_class.return_value = mock_repo
        mock_logger = Mock()
        mock_get_logger.return_value = mock_logger

        # Mock helper methods - submit_registration fails without captcha/sms
        with (
            patch.object(pipeline_service, "_select_proxy", return_value="http://proxy:8080"),
            patch.object(pipeline_service, "_fetch_signup_form", return_value=True),
            patch.object(pipeline_service, "_submit_registration", return_value=(False, False, False)),
        ):

            # Execute
            pipeline_service.run(mock_session, mock_task)

            # Verify failure state was set
            mock_repo.update_state.assert_called_with(
                mock_task.id,
                state="failed",
                step="submit_registration",
                result_json=json.dumps({"error": "SUBMIT_FAIL"}),
            )

    @patch("backend.app.services.registration_pipeline.create_proxy_provider")
    def test_select_proxy_success(self, mock_create_provider, pipeline_service):
        """测试代理选择成功"""
        # Setup mock provider
        mock_provider = Mock()
        mock_provider.next.return_value = "http://proxy:8080"
        mock_create_provider.return_value = mock_provider

        # Execute
        result = pipeline_service._select_proxy()

        # Verify
        assert result == "http://proxy:8080"
        mock_create_provider.assert_called_once_with("custom", {"proxies": ""})
        mock_provider.next.assert_called_once()

    @patch("backend.app.services.registration_pipeline.create_proxy_provider")
    def test_select_proxy_exception(self, mock_create_provider, pipeline_service):
        """测试代理选择异常情况"""
        # Setup mock to raise exception
        mock_create_provider.side_effect = Exception("Provider error")

        # Execute
        result = pipeline_service._select_proxy()

        # Verify
        assert result is None

    @patch("backend.app.services.registration_pipeline.requests.get")
    def test_fetch_signup_form_success(self, mock_get, pipeline_service):
        """测试成功获取注册表单"""
        # Setup mock response
        mock_response = Mock()
        mock_response.status_code = 200
        mock_response.headers = {"Content-Type": "text/html; charset=utf-8"}
        mock_get.return_value = mock_response

        # Execute
        result = pipeline_service._fetch_signup_form("http://proxy:8080")

        # Verify
        assert result is True
        mock_get.assert_called_once_with(
            "https://signup.live.com/",
            headers={"User-Agent": "Mozilla/5.0"},
            timeout=15,
            proxies={"http": "http://proxy:8080", "https": "http://proxy:8080"},
        )

    @patch("backend.app.services.registration_pipeline.requests.get")
    def test_fetch_signup_form_failure(self, mock_get, pipeline_service):
        """测试获取注册表单失败"""
        # Setup mock response with failure
        mock_response = Mock()
        mock_response.status_code = 404
        mock_get.return_value = mock_response

        # Execute
        result = pipeline_service._fetch_signup_form("http://proxy:8080")

        # Verify
        assert result is False

    @patch("backend.app.services.registration_pipeline.requests.get")
    def test_fetch_signup_form_exception(self, mock_get, pipeline_service):
        """测试获取注册表单网络异常"""
        # Setup mock to raise exception
        mock_get.side_effect = Exception("Network error")

        # Execute
        result = pipeline_service._fetch_signup_form("http://proxy:8080")

        # Verify
        assert result is False

    @patch("backend.app.services.registration_pipeline.requests.get")
    @patch("backend.app.services.registration_pipeline.time.sleep")
    def test_submit_registration_success(self, mock_sleep, mock_get, pipeline_service):
        """测试提交注册成功"""
        # Setup mock response
        mock_response = Mock()
        mock_response.status_code = 200
        mock_get.return_value = mock_response

        # Execute
        ok, need_captcha, need_sms = pipeline_service._submit_registration("http://proxy:8080")

        # Verify
        assert ok is True
        assert need_captcha is False
        assert need_sms is False
        mock_get.assert_called_once()
        mock_sleep.assert_not_called()  # Should not sleep on first success

    @patch("backend.app.services.registration_pipeline.requests.get")
    @patch("backend.app.services.registration_pipeline.time.sleep")
    def test_submit_registration_with_retry(self, mock_sleep, mock_get, pipeline_service):
        """测试提交注册重试机制"""
        # Setup mock responses - first two fail, third succeeds
        mock_responses = [Mock(), Mock(), Mock()]
        mock_responses[0].status_code = 500
        mock_responses[1].status_code = 500
        mock_responses[2].status_code = 200
        mock_get.side_effect = mock_responses

        # Execute
        ok, need_captcha, need_sms = pipeline_service._submit_registration("http://proxy:8080")

        # Verify
        assert ok is True
        assert mock_get.call_count == 3
        assert mock_sleep.call_count == 2  # Should sleep after first two failures

    @patch("backend.app.services.registration_pipeline.requests.get")
    @patch("backend.app.services.registration_pipeline.time.sleep")
    def test_submit_registration_all_retries_fail(self, mock_sleep, mock_get, pipeline_service):
        """测试提交注册所有重试都失败"""
        # Setup mock to always fail
        mock_response = Mock()
        mock_response.status_code = 500
        mock_get.return_value = mock_response

        # Execute
        ok, need_captcha, need_sms = pipeline_service._submit_registration("http://proxy:8080")

        # Verify
        assert ok is False
        assert need_captcha is False
        assert need_sms is False
        assert mock_get.call_count == 3  # Should retry 3 times
        assert mock_sleep.call_count == 2  # Should sleep after first two failures, not after final failure

    def test_submit_registration_no_proxy(self, pipeline_service):
        """测试没有代理的情况下提交注册"""
        with patch("backend.app.services.registration_pipeline.requests.get") as mock_get:
            mock_response = Mock()
            mock_response.status_code = 200
            mock_get.return_value = mock_response

            # Execute
            ok, need_captcha, need_sms = pipeline_service._submit_registration(None)

            # Verify no proxy was used
            mock_get.assert_called_with(
                "https://signup.live.com/API/CreateAccount",
                headers={"User-Agent": "Mozilla/5.0", "Accept": "application/json, text/plain, */*"},
                timeout=10,
                proxies=None,
            )
