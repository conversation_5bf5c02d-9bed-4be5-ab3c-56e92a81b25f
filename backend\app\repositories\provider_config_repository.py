from __future__ import annotations

import builtins

from sqlmodel import Session, select

from backend.app.db.models import ProviderConfig


class ProviderConfigRepository:
    def __init__(self, session: Session) -> None:
        self.session = session

    def upsert(self, provider_type: str, name: str, config_encrypted: str, is_active: bool = True) -> ProviderConfig:
        existing = self.get_by_type_name(provider_type, name)
        if existing:
            existing.config_encrypted = config_encrypted
            existing.is_active = is_active
            self.session.add(existing)
            self.session.commit()
            self.session.refresh(existing)
            return existing
        item = ProviderConfig(
            provider_type=provider_type, name=name, config_encrypted=config_encrypted, is_active=is_active
        )
        self.session.add(item)
        self.session.commit()
        self.session.refresh(item)
        return item

    def get(self, config_id: int) -> ProviderConfig | None:
        return self.session.get(ProviderConfig, config_id)

    def get_by_type_name(self, provider_type: str, name: str) -> ProviderConfig | None:
        statement = select(ProviderConfig).where(
            ProviderConfig.provider_type == provider_type, ProviderConfig.name == name
        )
        return self.session.exec(statement).first()

    def list(self, provider_type: str | None = None, active_only: bool = False) -> builtins.list[ProviderConfig]:
        statement = select(ProviderConfig)
        if provider_type:
            statement = statement.where(ProviderConfig.provider_type == provider_type)
        if active_only:
            statement = statement.where(ProviderConfig.is_active == True)  # noqa: E712
        statement = statement.order_by(ProviderConfig.id.desc())
        return list(self.session.exec(statement))
