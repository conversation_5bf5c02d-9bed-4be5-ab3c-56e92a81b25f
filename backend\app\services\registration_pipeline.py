from __future__ import annotations

import json
import logging
import time

import requests
from sqlmodel import Session

from backend.app.core.logging import log_event
from backend.app.db.models import Task
from backend.app.providers.factory import create_proxy_provider
from backend.app.repositories.task_repository import TaskRepository


class RegistrationPipelineService:
    """
    MVP占位：模拟注册步骤执行。后续在P0内逐步用真实逻辑替换各步骤。
    """

    def run(self, session: Session, task: Task) -> None:
        repo = TaskRepository(session)
        logger = logging.getLogger(__name__)

        # Step 1: select_proxy
        step = "select_proxy"
        repo.update_state(task.id, state="running", step=step)
        log_event(logger, event="pipeline_step", task_id=task.id, step=step, outcome="running")
        proxy = self._select_proxy()
        log_event(
            logger, event="proxy_selected", task_id=task.id, step=step, outcome="ok", extra={"proxy": proxy or "none"}
        )

        # Step 2: fetch_signup_form (placeholder network call)
        step = "fetch_signup_form"
        repo.update_state(task.id, state="running", step=step)
        log_event(logger, event="pipeline_step", task_id=task.id, step=step, outcome="running")
        fetched_ok = self._fetch_signup_form(proxy)
        if not fetched_ok:
            repo.update_state(task.id, state="failed", step=step, result_json=json.dumps({"error": "FETCH_FORM_FAIL"}))
            log_event(
                logger,
                event="pipeline_fail",
                task_id=task.id,
                step=step,
                outcome="failed",
                error_code="FETCH_FORM_FAIL",
            )
            return

        # Step 3: submit_registration (placeholder with backoff)
        step = "submit_registration"
        repo.update_state(task.id, state="running", step=step)
        log_event(logger, event="pipeline_step", task_id=task.id, step=step, outcome="running")
        ok, need_captcha, need_sms = self._submit_registration(proxy)
        if not ok and not (need_captcha or need_sms):
            repo.update_state(task.id, state="failed", step=step, result_json=json.dumps({"error": "SUBMIT_FAIL"}))
            log_event(
                logger, event="pipeline_fail", task_id=task.id, step=step, outcome="failed", error_code="SUBMIT_FAIL"
            )
            return

        # Remaining steps still placeholder
        for step in [
            "solve_captcha_if_needed",
            "verify_phone_if_needed",
            "enable_imap_pop3",
            "fetch_oauth_token",
        ]:
            repo.update_state(task.id, state="running", step=step)
            log_event(logger, event="pipeline_step", task_id=task.id, step=step, outcome="running")
            time.sleep(0.2)

        repo.update_state(task.id, state="succeeded", step="done", result_json=json.dumps({"ok": True}))
        log_event(logger, event="pipeline_done", task_id=task.id, step="done", outcome="succeeded")

    # --- helpers ---
    def _select_proxy(self) -> str | None:
        # MVP: proxies via environment/config later; allow empty list
        try:
            provider = create_proxy_provider("custom", {"proxies": ""})
            return provider.next()
        except Exception:
            return None

    def _fetch_signup_form(self, proxy: str | None) -> bool:
        try:
            proxies = {"http": proxy, "https": proxy} if proxy else None
            headers = {"User-Agent": "Mozilla/5.0"}
            r = requests.get("https://signup.live.com/", headers=headers, timeout=15, proxies=proxies)
            return r.status_code == 200 and "html" in r.headers.get("Content-Type", "").lower()
        except Exception:
            return False

    def _submit_registration(self, proxy: str | None) -> tuple[bool, bool, bool]:
        """
        Return: (ok, need_captcha, need_sms)
        MVP: 不真正提交，仅做占位与退避策略示例。
        """
        proxies = {"http": proxy, "https": proxy} if proxy else None
        headers = {"User-Agent": "Mozilla/5.0", "Accept": "application/json, text/plain, */*"}
        # 占位：尝试访问一个稳定地址模拟成功/失败（这里用 signup.live.com 再次探活）
        backoff = [0.2, 0.5, 1.0]
        for i, delay in enumerate(backoff):
            try:
                r = requests.get(
                    "https://signup.live.com/API/CreateAccount", headers=headers, timeout=10, proxies=proxies
                )
                # 真实实现会 POST 并解析 JSON；此处以非200模拟触发后续 captcha/sms 分支
                if r.status_code == 200:
                    return True, False, False
                # 简易分支：偶数重试视为需要验证码，奇数重试视为需要短信（仅占位）
                if i == len(backoff) - 1:
                    return False, False, False
                time.sleep(delay)
            except Exception:
                if i == len(backoff) - 1:
                    return False, False, False
                time.sleep(delay)
        return False, False, False
