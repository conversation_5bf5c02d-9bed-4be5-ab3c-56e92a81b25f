# Auto EM v2 API 文档

## 概览

Auto EM v2 提供 RESTful API 接口，支持邮箱注册、账户养护、群发邮件等功能。

**基础地址**: `http://localhost:8000`  
**API 版本**: `v2.0.0`  
**文档地址**: `http://localhost:8000/docs` (Swagger UI)  
**OpenAPI Schema**: `http://localhost:8000/openapi.json`

## 认证

目前版本暂不包含认证机制，所有接口均为公开访问。

## 响应格式

### 成功响应
```json
{
  "id": 123,
  "state": "pending",
  "message": "Task created successfully"
}
```

### 错误响应
```json
{
  "error": "error_code",
  "message": "Error description"
}
```

### 通用响应
```json
{
  "ok": true,
  "data": {...}
}
```

## 核心 API

### 系统状态

#### 健康检查
- **URL**: `/health`
- **方法**: `GET`
- **描述**: 检查系统健康状态

**响应示例**:
```json
{
  "status": "ok",
  "env": "production"
}
```

---

## 任务管理 API

### 注册任务

#### 启动邮箱注册任务
- **URL**: `/tasks/start/reg`
- **方法**: `POST`
- **描述**: 创建新的邮箱注册任务

**请求体**:
```json
{
  "email": "<EMAIL>",
  "password": "optional_password",
  "proxy": "optional_proxy_url"
}
```

**响应示例**:
```json
{
  "id": 123,
  "state": "pending"
}
```

### 养护任务

#### 启动账户养护任务
- **URL**: `/tasks/start/nurture`
- **方法**: `POST`
- **描述**: 创建账户养护任务

**请求体**:
```json
{
  "account_id": 1,
  "nurture_type": "login_check",  // login_check, mark_emails_read, update_profile, maintenance
  "max_emails": 10,               // 仅对 mark_emails_read 有效
  "update_type": "avatar",        // 仅对 update_profile 有效: avatar, nickname, signature
  "avatar_path": "/path/to/avatar.jpg",
  "nickname": "新昵称",
  "signature": "个性签名",
  "tasks": ["login_check", "mark_emails_read"]  // 仅对 maintenance 有效
}
```

**响应示例**:
```json
{
  "id": 124,
  "state": "pending"
}
```

**养护类型说明**:
- `login_check`: 登录检查
- `mark_emails_read`: 标记邮件已读
- `update_profile`: 更新账户资料
- `maintenance`: 综合维护

### 群发邮件任务

#### 启动群发邮件任务
- **URL**: `/tasks/start/bulk_mail`
- **方法**: `POST`
- **描述**: 创建群发邮件任务

**请求体**:
```json
{
  "template": {
    "subject": "欢迎 {recipient}！",
    "content": "你好 {recipient}，\n\n您是第 {sequence} 位用户。\n\n发送时间：{date} {time}",
    "variables": {}
  },
  "recipients": [
    "<EMAIL>",
    "<EMAIL>",
    "<EMAIL>"
  ],
  "account_pool": [
    "<EMAIL>",
    "<EMAIL>"
  ],
  "rate_limit": {
    "emails_per_hour": 100,
    "emails_per_day": 1000,
    "delay_between_emails": 30.0,
    "randomize_delay": true,
    "delay_variance": 0.3
  }
}
```

**响应示例**:
```json
{
  "id": 125,
  "state": "pending"
}
```

**模板变量**:
- `{recipient}`: 收件人地址
- `{sequence}`: 邮件序号
- `{date}`: 当前日期
- `{time}`: 当前时间

### 任务查询

#### 查询单个任务
- **URL**: `/tasks/{task_id}`
- **方法**: `GET`
- **描述**: 查询指定任务状态

**响应示例**:
```json
{
  "id": 123,
  "kind": "reg",
  "state": "running",
  "step": "fetch_signup_form"
}
```

#### 查询任务列表
- **URL**: `/tasks`
- **方法**: `GET`
- **描述**: 查询任务列表

**查询参数**:
- `kind` (可选): 任务类型 (`reg`, `nurture`, `bulk_mail`)
- `state` (可选): 任务状态 (`pending`, `running`, `succeeded`, `failed`)

**响应示例**:
```json
[
  {
    "id": 123,
    "kind": "reg",
    "state": "succeeded",
    "step": "done"
  },
  {
    "id": 124,
    "kind": "nurture",
    "state": "running",
    "step": "login_check"
  }
]
```

**任务状态**:
- `pending`: 等待执行
- `running`: 执行中
- `succeeded`: 成功完成
- `failed`: 执行失败

---

## Provider 测试 API

#### 测试Provider连接
- **URL**: `/providers/test`
- **方法**: `POST`
- **描述**: 测试Provider服务连通性

**请求体**:
```json
{
  "type": "captcha",  // captcha, sms, proxy
  "name": "2captcha",
  "config": {
    "api_key": "your_api_key_here"
  }
}
```

**响应示例**:
```json
{
  "ok": true,
  "diagnostics": {
    "balance": 10.50,
    "status": "active"
  }
}
```

---

## 资料管理 API

#### 获取资料库统计
- **URL**: `/profile/stats`
- **方法**: `GET`
- **描述**: 获取头像、昵称、签名等资料库统计

**响应示例**:
```json
{
  "ok": true,
  "stats": {
    "avatars": 100,
    "nicknames": 500,
    "signatures": 200
  }
}
```

#### 获取随机资料
- **URL**: `/profile/random`
- **方法**: `GET`
- **描述**: 获取随机头像、昵称或签名

**查询参数**:
- `profile_type`: 资料类型 (`avatar`, `nickname`, `signature`)

**响应示例**:
```json
{
  "ok": true,
  "data": "阳光小屋"
}
```

#### 清理临时文件
- **URL**: `/profile/cleanup`
- **方法**: `POST`
- **描述**: 清理过期的临时文件

**响应示例**:
```json
{
  "ok": true,
  "message": "cleanup_completed"
}
```

---

## 群发邮件管理 API

#### 获取群发任务统计
- **URL**: `/bulk_mail/stats/{task_id}`
- **方法**: `GET`
- **描述**: 获取群发任务实时统计信息

**响应示例**:
```json
{
  "ok": true,
  "stats": {
    "total_emails": 1000,
    "sent_emails": 750,
    "failed_emails": 50,
    "pending_emails": 200,
    "accounts_used": 5,
    "start_time": "2024-08-29T10:00:00Z",
    "end_time": null
  }
}
```

#### 验证邮件模板
- **URL**: `/bulk_mail/templates/validate`
- **方法**: `POST`
- **描述**: 验证邮件模板格式和变量

**请求体**:
```json
{
  "subject": "欢迎 {recipient}！",
  "content": "你好 {recipient}，您是第 {sequence} 位用户。"
}
```

**响应示例**:
```json
{
  "ok": true,
  "template_variables": ["recipient", "sequence"],
  "supported_variables": ["recipient", "sequence", "date", "time"],
  "unsupported_variables": [],
  "warnings": []
}
```

#### 获取示例模板
- **URL**: `/bulk_mail/templates/example`
- **方法**: `GET`
- **描述**: 获取预定义的邮件模板示例

**响应示例**:
```json
{
  "ok": true,
  "examples": {
    "welcome": {
      "name": "欢迎邮件",
      "template": {
        "subject": "欢迎加入我们！- {date}",
        "content": "你好 {recipient}，\n\n欢迎加入我们的社区！...",
        "variables": {}
      }
    },
    "newsletter": {...},
    "promotion": {...}
  }
}
```

---

## 错误代码

### 通用错误
- `payload_required`: 缺少请求体
- `invalid_format`: 格式错误
- `internal_error`: 内部服务器错误

### 任务相关错误
- `failed_to_start`: 任务启动失败
- `task_not_found`: 任务不存在
- `account_id_required`: 缺少账户ID
- `template_required`: 缺少邮件模板
- `recipients_required`: 缺少收件人列表
- `account_pool_required`: 缺少发件账户池

### Provider相关错误
- `unsupported_type`: 不支持的Provider类型
- `provider_error`: Provider服务错误
- `connection_failed`: 连接失败

### 资料管理错误
- `no_data_available`: 没有可用数据
- `cleanup_failed`: 清理失败

---

## 限制说明

### 速率限制
目前版本暂未实现全局速率限制，但群发邮件任务内置了发送频率控制。

### 数据限制
- 邮件模板内容最大 64KB
- 收件人列表最大 10,000 个
- 账户池最大 100 个

---

## SDK 和集成

### cURL 示例

#### 启动注册任务
```bash
curl -X POST "http://localhost:8000/tasks/start/reg" \
  -H "Content-Type: application/json" \
  -d '{"email": "<EMAIL>"}'
```

#### 查询任务状态
```bash
curl "http://localhost:8000/tasks/123"
```

#### 启动群发任务
```bash
curl -X POST "http://localhost:8000/tasks/start/bulk_mail" \
  -H "Content-Type: application/json" \
  -d '{
    "template": {
      "subject": "Hello {recipient}",
      "content": "Welcome to our service!"
    },
    "recipients": ["<EMAIL>"],
    "account_pool": ["<EMAIL>"]
  }'
```

### Python 示例

```python
import requests

# 启动注册任务
response = requests.post(
    "http://localhost:8000/tasks/start/reg",
    json={"email": "<EMAIL>"}
)
task = response.json()
print(f"Task ID: {task['id']}")

# 查询任务状态
response = requests.get(f"http://localhost:8000/tasks/{task['id']}")
status = response.json()
print(f"Status: {status['state']}")
```

### JavaScript 示例

```javascript
// 启动注册任务
const response = await fetch('http://localhost:8000/tasks/start/reg', {
  method: 'POST',
  headers: {
    'Content-Type': 'application/json',
  },
  body: JSON.stringify({
    email: '<EMAIL>'
  })
});

const task = await response.json();
console.log(`Task ID: ${task.id}`);

// 查询任务状态
const statusResponse = await fetch(`http://localhost:8000/tasks/${task.id}`);
const status = await statusResponse.json();
console.log(`Status: ${status.state}`);
```

---

## 调试和故障排除

### 常见问题

1. **任务一直处于pending状态**
   - 检查后台worker是否运行
   - 查看日志文件确认错误信息

2. **Provider测试失败**
   - 验证API密钥是否正确
   - 检查网络连接
   - 确认Provider服务状态

3. **群发邮件任务失败**
   - 确认账户池中的账户状态为active
   - 检查邮件模板格式
   - 验证收件人地址格式

### 日志查看

```bash
# 查看应用日志
tail -f logs/auto-em-v2.log

# 启动调试模式
python start.py server --reload
```

### 性能监控

访问 `/health` 端点可以检查系统基本状态，更详细的监控可以通过日志分析实现。

---

## 版本更新

API版本遵循语义化版本控制。重大变更会更新主版本号，向后兼容的新功能会更新次版本号。

当前版本: **v2.0.0**

---

更多技术细节请参考源码注释和OpenAPI文档 (`/docs`)。