import logging

from fastapi import FastAPI

try:
    # Optional: initialize logging if available
    from backend.app.core.logging import configure_logging  # type: ignore

    configure_logging()
except Exception:
    # Fallback: basic logging if core logging isn't available yet
    logging.basicConfig(level=logging.INFO, format="%(asctime)s %(levelname)s %(name)s - %(message)s")

try:
    from backend.app.core.config import settings  # type: ignore
except Exception:

    class _FallbackSettings:
        app_name: str = "Auto EM v2"
        environment: str = "dev"

    settings = _FallbackSettings()  # type: ignore


app = FastAPI(
    title=getattr(settings, "app_name", "Auto EM v2"),
    description="自动邮箱注册工具 v2 - 多Provider支持与任务化引擎",
    version="2.0.0",
)


# 应用启动事件
@app.on_event("startup")
async def startup_event() -> None:
    """应用启动时的初始化"""
    logger = logging.getLogger(__name__)
    logger.info("Starting Auto EM v2...")

    try:
        # 确保数据库表存在
        from sqlmodel import SQLModel

        from backend.app.db.session import engine

        logger.info("Creating database tables if not exist...")
        SQLModel.metadata.create_all(engine)
        logger.info("Database initialization completed")

    except Exception as e:
        logger.error(f"Database initialization failed: {e}")
        # 不阻止应用启动，让应用继续运行但记录错误


@app.on_event("shutdown")
async def shutdown_event() -> None:
    """应用关闭时的清理"""
    logger = logging.getLogger(__name__)
    logger.info("Shutting down Auto EM v2...")


@app.get("/health")
def health_check() -> dict:
    return {"status": "ok", "env": getattr(settings, "environment", "dev")}


# --- Minimal Tasks API ---
from sqlmodel import Session  # type: ignore

from backend.app.db.session import (
    engine,  # type: ignore
    init_db,  # type: ignore
)
from backend.app.providers.base import CaptchaProvider, ProxyProvider, SmsProvider  # type: ignore
from backend.app.providers.factory import (  # type: ignore
    create_captcha_provider,
    create_proxy_provider,
    create_sms_provider,
)
from backend.app.services.registration_pipeline import RegistrationPipelineService  # type: ignore
from backend.app.services.task_engine import TaskEngine  # type: ignore

init_db()
engine_lock = None  # placeholder if we need synchronization later


def session_factory() -> Session:
    return Session(engine)


engine_instance = TaskEngine(session_factory=session_factory)
engine_instance.register_runner("reg", RegistrationPipelineService().run)

# 注册养护服务
try:
    from backend.app.services.nurture_service import NurtureService  # type: ignore

    engine_instance.register_runner("nurture", NurtureService().run)
except ImportError:
    logging.getLogger(__name__).warning("NurtureService not available - nurture tasks will not work")

# 注册群发服务
try:
    from backend.app.services.bulk_mail_service import BulkMailService  # type: ignore

    bulk_mail_service = BulkMailService()
    engine_instance.register_runner("bulk_mail", bulk_mail_service.run)
except ImportError:
    logging.getLogger(__name__).warning("BulkMailService not available - bulk mail tasks will not work")


@app.post("/tasks/start/reg")
def start_reg_task(payload: dict | None = None) -> dict[str, str | int]:
    task = engine_instance.start("reg", payload)
    return {"id": task.id, "state": task.state}


@app.post("/tasks/start/nurture")
def start_nurture_task(payload: dict | None = None) -> dict[str, str | int]:
    """启动养护任务"""
    if not payload:
        return {"error": "payload_required"}

    # 验证必需参数
    if "account_id" not in payload:
        return {"error": "account_id_required"}

    # 设置默认养护类型
    if "nurture_type" not in payload:
        payload["nurture_type"] = "login_check"

    try:
        task = engine_instance.start("nurture", payload)
        return {"id": task.id, "state": task.state}
    except Exception as e:
        return {"error": "failed_to_start", "message": str(e)}


@app.post("/tasks/start/bulk_mail")
def start_bulk_mail_task(payload: dict | None = None) -> dict[str, str | int]:
    """启动群发邮件任务"""
    if not payload:
        return {"error": "payload_required"}

    # 验证必需参数
    required_fields = ["template", "recipients", "account_pool"]
    for field in required_fields:
        if field not in payload:
            return {"error": f"{field}_required"}

    # 验证模板格式
    template = payload.get("template")
    if not isinstance(template, dict) or "subject" not in template or "content" not in template:
        return {"error": "invalid_template_format"}

    # 验证收件人列表
    recipients = payload.get("recipients")
    if not isinstance(recipients, list) or len(recipients) == 0:
        return {"error": "invalid_recipients_list"}

    # 验证账号池
    account_pool = payload.get("account_pool")
    if not isinstance(account_pool, list) or len(account_pool) == 0:
        return {"error": "invalid_account_pool"}

    try:
        task = engine_instance.start("bulk_mail", payload)
        return {"id": task.id, "state": task.state}
    except Exception as e:
        return {"error": "failed_to_start", "message": str(e)}


@app.get("/tasks/{task_id}")
def get_task(task_id: int) -> dict[str, str | int]:
    task = engine_instance.get(task_id)
    if not task:
        return {"error": "not_found"}
    return {"id": task.id, "kind": task.kind, "state": task.state, "step": task.step}


@app.get("/tasks")
def list_tasks(kind: str | None = None, state: str | None = None) -> list[dict[str, str | int]]:
    items = engine_instance.list(kind=kind, state=state)
    return [{"id": t.id, "kind": t.kind, "state": t.state, "step": t.step} for t in items]


# --- Providers test endpoint ---
@app.post("/providers/test")
def test_provider(payload: dict) -> dict[str, str | bool | dict]:
    ptype = (payload.get("type") or "").lower()  # captcha|sms|proxy
    name = payload.get("name") or ""
    config = payload.get("config") or {}
    prov: CaptchaProvider | SmsProvider | ProxyProvider
    if ptype == "captcha":
        prov = create_captcha_provider(name, config)
    elif ptype == "sms":
        prov = create_sms_provider(name, config)
    elif ptype == "proxy":
        prov = create_proxy_provider(name, config)
    else:
        return {"ok": False, "error": "unsupported_type"}
    try:
        res = prov.test_connectivity()
        ok = bool(res.get("ok"))
        return {"ok": ok, "diagnostics": res}
    except Exception as e:
        return {"ok": False, "error": str(e)}


# --- 资料管理API ---
@app.get("/profile/stats")
def get_profile_stats() -> dict[str, str | bool | dict]:
    """获取资料库统计信息"""
    try:
        from backend.app.services.profile_manager import ProfileManager

        manager = ProfileManager()
        stats = manager.get_stats()
        return {"ok": True, "stats": stats}
    except Exception as e:
        return {"ok": False, "error": str(e)}


@app.get("/profile/random")
def get_random_profile(profile_type: str = "nickname") -> dict[str, str | bool]:
    """获取随机资料"""
    try:
        from backend.app.services.profile_manager import ProfileManager

        manager = ProfileManager()

        if profile_type == "nickname":
            result = manager.get_random_nickname()
        elif profile_type == "avatar":
            result = manager.get_random_avatar()
        elif profile_type == "signature":
            result = manager.get_random_signature()
        else:
            return {"ok": False, "error": "unsupported_profile_type"}

        if result:
            return {"ok": True, "data": result}
        else:
            return {"ok": False, "error": "no_data_available"}

    except Exception as e:
        return {"ok": False, "error": str(e)}


@app.post("/profile/cleanup")
def cleanup_temp_files() -> dict[str, str | bool | int]:
    """清理临时文件"""
    try:
        from backend.app.services.profile_manager import ProfileManager

        manager = ProfileManager()
        manager.cleanup_temp_files()
        return {"ok": True, "message": "cleanup_completed"}
    except Exception as e:
        return {"ok": False, "error": str(e)}


# --- 群发邮件管理API ---
@app.get("/bulk_mail/stats/{task_id}")
def get_bulk_mail_stats(task_id: int) -> dict[str, str | bool | dict]:
    """获取群发任务统计"""
    try:
        stats = bulk_mail_service.get_task_stats(task_id)
        if stats:
            return {
                "ok": True,
                "stats": {
                    "total_emails": stats.total_emails,
                    "sent_emails": stats.sent_emails,
                    "failed_emails": stats.failed_emails,
                    "pending_emails": stats.pending_emails,
                    "accounts_used": stats.accounts_used,
                    "start_time": stats.start_time.isoformat() if stats.start_time else None,
                    "end_time": stats.end_time.isoformat() if stats.end_time else None,
                },
            }
        else:
            return {"ok": False, "error": "task_not_found_or_inactive"}
    except Exception as e:
        return {"ok": False, "error": str(e)}


@app.post("/bulk_mail/templates/validate")
def validate_bulk_mail_template(template: dict) -> dict[str, str | bool | list]:
    """验证群发邮件模板"""
    try:
        # 检查必需字段
        if not isinstance(template, dict):
            return {"ok": False, "error": "template_must_be_object"}

        required_fields = ["subject", "content"]
        for field in required_fields:
            if field not in template:
                return {"ok": False, "error": f"missing_field_{field}"}

        # 检查字段类型
        if not isinstance(template["subject"], str) or not isinstance(template["content"], str):
            return {"ok": False, "error": "subject_and_content_must_be_strings"}

        # 检查模板变量
        import re

        all_text = template["subject"] + " " + template["content"]
        template_vars = re.findall(r"\{(\w+)\}", all_text)
        supported_vars = ["recipient", "sequence", "date", "time"]

        unsupported_vars = [var for var in template_vars if var not in supported_vars]

        result = {
            "ok": True,
            "template_variables": template_vars,
            "supported_variables": supported_vars,
            "unsupported_variables": unsupported_vars,
        }

        if unsupported_vars:
            result["warnings"] = [f"Unsupported variable: {var}" for var in unsupported_vars]

        return result

    except Exception as e:
        return {"ok": False, "error": str(e)}


@app.get("/bulk_mail/templates/example")
def get_example_templates() -> dict[str, str | bool | dict]:
    """获取示例邮件模板"""
    try:
        examples = {
            "welcome": {
                "name": "欢迎邮件",
                "template": {
                    "subject": "欢迎加入我们！- {date}",
                    "content": """你好 {recipient}，

欢迎加入我们的社区！你是第 {sequence} 位加入的成员。

我们很高兴你能成为我们的一员。在这里你可以：
- 获得最新的产品信息
- 参与社区讨论
- 享受专属优惠

如有任何问题，请随时联系我们。

祝您使用愉快！

发送时间：{time}""",
                    "variables": {},
                },
            },
            "newsletter": {
                "name": "新闻通讯",
                "template": {
                    "subject": "📧 本周精选内容 - {date}",
                    "content": """亲爱的 {recipient}，

本周为您精选了以下内容：

🎯 热门话题
• 话题一：最新行业动态
• 话题二：技术发展趋势
• 话题三：市场分析报告

🔥 推荐阅读
• 文章一：如何提高工作效率
• 文章二：数字化转型指南
• 文章三：创新思维培养

感谢您的关注，这是您收到的第 {sequence} 期内容。

Best regards,
团队

发送于 {date} {time}""",
                    "variables": {},
                },
            },
            "promotion": {
                "name": "促销活动",
                "template": {
                    "subject": "🎉 限时优惠！仅限今日 - {date}",
                    "content": """Hi {recipient}，

🔥 特大优惠来啦！

今日限时活动：
✅ 全场商品8折优惠
✅ 满299元包邮
✅ 新用户额外享受5%折扣

⏰ 活动时间：{date} 24:00前
📦 您是第 {sequence} 位收到此优惠的用户

立即购买链接：[点击这里]

机不可失，时不再来！

购物愉快，
销售团队
{time}""",
                    "variables": {},
                },
            },
        }

        return {"ok": True, "examples": examples}

    except Exception as e:
        return {"ok": False, "error": str(e)}


if __name__ == "__main__":
    # Running via: python backend/main.py (requires uvicorn installed)
    try:
        import uvicorn  # type: ignore

        uvicorn.run(app, host="127.0.0.1", port=8000, log_level="info")
    except Exception as e:
        import logging

        logging.getLogger(__name__).error("Unable to start uvicorn: %s", e)
        print("Install uvicorn to run the API locally: pip install uvicorn[standard]")
