"""
Task Repository 单元测试
"""
import pytest
from unittest.mock import Mock, patch
from datetime import datetime

from backend.app.repositories.task_repository import TaskRepository
from backend.app.db.models import Task


class TestTaskRepository:
    """TaskRepository 测试"""

    @pytest.fixture
    def mock_session(self):
        """Mock数据库会话"""
        return Mock()

    @pytest.fixture
    def task_repo(self, mock_session):
        """TaskRepository实例"""
        return TaskRepository(mock_session)

    @pytest.fixture
    def sample_task(self):
        """示例任务"""
        return Task(
            id=1,
            kind="reg",
            state="running",
            step="fetch_form",
            payload={"email": "<EMAIL>"},
            created_at=datetime.now(),
            updated_at=datetime.now()
        )

    def test_init(self, mock_session):
        """测试初始化"""
        repo = TaskRepository(mock_session)
        assert repo.session == mock_session

    def test_create_task(self, task_repo, mock_session):
        """测试创建任务"""
        payload_json = '{"email": "<EMAIL>"}'

        # Mock session.add and commit
        mock_session.add = Mock()
        mock_session.commit = Mock()
        mock_session.refresh = Mock()

        result = task_repo.create("reg", payload_json)

        # 验证调用
        mock_session.add.assert_called_once()
        mock_session.commit.assert_called_once()
        mock_session.refresh.assert_called_once()

        # 验证任务属性
        assert result.kind == "reg"
        assert result.state == "pending"
        assert result.step is None
        assert result.payload_json == payload_json

    def test_get_found(self, task_repo, mock_session, sample_task):
        """测试通过ID获取任务 - 找到"""
        mock_session.get.return_value = sample_task

        result = task_repo.get(1)

        assert result == sample_task
        mock_session.get.assert_called_once_with(Task, 1)

    def test_get_not_found(self, task_repo, mock_session):
        """测试通过ID获取任务 - 未找到"""
        mock_session.get.return_value = None

        result = task_repo.get(999)

        assert result is None
        mock_session.get.assert_called_once_with(Task, 999)

    def test_update_state(self, task_repo, mock_session, sample_task):
        """测试更新任务状态"""
        mock_session.get.return_value = sample_task
        mock_session.commit = Mock()
        mock_session.refresh = Mock()
        
        result = task_repo.update_state(1, "completed", "finished")
        
        # 验证状态更新
        assert sample_task.state == "completed"
        assert sample_task.step == "finished"
        assert result == sample_task
        
        # 验证数据库操作
        mock_session.commit.assert_called_once()
        mock_session.refresh.assert_called_once()

    def test_update_state_task_not_found(self, task_repo, mock_session):
        """测试更新不存在任务的状态"""
        mock_session.get.return_value = None
        
        with pytest.raises(ValueError, match="Task 999 not found"):
            task_repo.update_state(999, "completed", "finished")

    def test_update_state_with_result(self, task_repo, mock_session, sample_task):
        """测试更新任务状态和结果"""
        mock_session.get.return_value = sample_task
        mock_session.add = Mock()
        mock_session.commit = Mock()
        mock_session.refresh = Mock()

        result_json = '{"success": true, "message": "completed"}'
        result = task_repo.update_state(1, "completed", "finished", result_json)

        # 验证状态更新
        assert sample_task.state == "completed"
        assert sample_task.step == "finished"
        assert sample_task.result_json == result_json
        assert result == sample_task

        # 验证数据库操作
        mock_session.add.assert_called_once()
        mock_session.commit.assert_called_once()
        mock_session.refresh.assert_called_once()

    @patch('backend.app.repositories.task_repository.select')
    def test_list_all_tasks(self, mock_select, task_repo, mock_session, sample_task):
        """测试列出所有任务"""
        # Mock select and exec
        mock_statement = Mock()
        mock_select.return_value = mock_statement
        mock_statement.order_by.return_value = mock_statement
        mock_statement.limit.return_value = mock_statement
        
        mock_session.exec.return_value = [sample_task]
        
        result = task_repo.list()
        
        # 验证结果
        assert result == [sample_task]
        
        # 验证SQL构建
        mock_select.assert_called_once_with(Task)
        mock_statement.limit.assert_called_once_with(100)

    @patch('backend.app.repositories.task_repository.select')
    def test_list_tasks_with_filters(self, mock_select, task_repo, mock_session, sample_task):
        """测试带过滤条件列出任务"""
        # Mock select and exec
        mock_statement = Mock()
        mock_select.return_value = mock_statement
        mock_statement.where.return_value = mock_statement
        mock_statement.order_by.return_value = mock_statement
        mock_statement.limit.return_value = mock_statement
        
        mock_session.exec.return_value = [sample_task]
        
        result = task_repo.list(kind="reg", state="running", limit=50)
        
        # 验证结果
        assert result == [sample_task]
        
        # 验证SQL构建
        mock_select.assert_called_once_with(Task)
        assert mock_statement.where.call_count == 2  # kind and state filters
        mock_statement.limit.assert_called_once_with(50)

    @patch('backend.app.repositories.task_repository.select')
    def test_list_tasks_with_kind_filter_only(self, mock_select, task_repo, mock_session, sample_task):
        """测试只按类型过滤任务"""
        # Mock select and exec
        mock_statement = Mock()
        mock_select.return_value = mock_statement
        mock_statement.where.return_value = mock_statement
        mock_statement.order_by.return_value = mock_statement
        mock_statement.limit.return_value = mock_statement
        
        mock_session.exec.return_value = [sample_task]
        
        result = task_repo.list(kind="reg")
        
        # 验证结果
        assert result == [sample_task]
        
        # 验证SQL构建
        mock_select.assert_called_once_with(Task)
        assert mock_statement.where.call_count == 1  # only kind filter

    @patch('backend.app.repositories.task_repository.select')
    def test_list_tasks_with_state_filter_only(self, mock_select, task_repo, mock_session, sample_task):
        """测试只按状态过滤任务"""
        # Mock select and exec
        mock_statement = Mock()
        mock_select.return_value = mock_statement
        mock_statement.where.return_value = mock_statement
        mock_statement.order_by.return_value = mock_statement
        mock_statement.limit.return_value = mock_statement
        
        mock_session.exec.return_value = [sample_task]
        
        result = task_repo.list(state="running")
        
        # 验证结果
        assert result == [sample_task]
        
        # 验证SQL构建
        mock_select.assert_called_once_with(Task)
        assert mock_statement.where.call_count == 1  # only state filter
