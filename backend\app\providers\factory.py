from __future__ import annotations

from typing import Any

from backend.app.providers.base import <PERSON><PERSON><PERSON><PERSON><PERSON>, ProxyProvider, SmsProvider
from backend.app.providers.implementations import (
    CustomProxyProvider,
    SmsActivateProvider,
    TwoCaptchaProvider,
)


def create_captcha_provider(name: str, config: dict[str, Any]) -> CaptchaProvider:
    if name.lower() in {"2captcha", "twocaptcha"}:
        return TwoCaptchaProvider(api_key=config.get("api_key", ""))
    raise ValueError(f"Unknown captcha provider: {name}")


def create_sms_provider(name: str, config: dict[str, Any]) -> SmsProvider:
    if name.lower() in {"sms-activate", "sms_activate", "smsactivate"}:
        return SmsActivateProvider(api_key=config.get("api_key", ""))
    raise ValueError(f"Unknown sms provider: {name}")


def create_proxy_provider(name: str, config: dict[str, Any]) -> ProxyProvider:
    if name.lower() in {"custom", "csv", "list"}:
        return CustomProxyProvider(proxies_csv=config.get("proxies", ""))
    raise ValueError(f"Unknown proxy provider: {name}")
