[tool:pytest]
minversion = 6.0
addopts = -ra -q --strict-markers
testpaths = tests
python_files = test_*.py
python_classes = Test*
python_functions = test_*
markers =
    unit: Unit tests (isolated, fast)
    integration: Integration tests (with external deps)
    contract: Contract tests for providers
    slow: Slow running tests
    network: Tests that require network access
filterwarnings =
    ignore::DeprecationWarning
    ignore::PendingDeprecationWarning