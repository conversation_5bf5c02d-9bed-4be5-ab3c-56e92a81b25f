import json
import logging
import time
from typing import Any


def configure_logging() -> None:
    logging.basicConfig(
        level=logging.INFO,
        format="%(asctime)s %(levelname)s %(name)s - %(message)s",
    )


def log_event(
    logger: logging.Logger,
    *,
    event: str,
    task_id: int | None = None,
    account_id: int | None = None,
    step: str | None = None,
    outcome: str | None = None,
    error_code: str | None = None,
    provider: str | None = None,
    extra: dict[str, Any] | None = None,
) -> None:
    payload = {
        "event": event,
        "task_id": task_id,
        "account_id": account_id,
        "step": step,
        "outcome": outcome,
        "error_code": error_code,
        "provider": provider,
        "ts": time.time(),
    }
    if extra:
        payload.update(extra)
    logger.info(json.dumps(payload, ensure_ascii=False))
